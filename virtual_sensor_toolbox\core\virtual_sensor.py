from abc import ABC, abstractmethod
from typing import List, Dict, Any

class VirtualSensor(ABC):
    """
    Abstract Base Class for all virtual sensor plugins.
    """

    @abstractmethod
    def get_name(self) -> str:
        """Returns the display name of the sensor."""
        pass

    @abstractmethod
    def get_description(self) -> str:
        """Returns a brief description of the sensor."""
        pass

    @abstractmethod
    def get_available_data_streams(self) -> List[str]:
        """
        Returns a list of identifiable data streams this sensor can simulate.
        Example: ["NMEA0183_SERIAL", "CMRx_TCPIP_PRIMARY", "NMEA0183_TCPIP_DEBUG"]
        """
        pass

    @abstractmethod
    def get_configuration_options(self) -> Dict[str, Any]:
        """
        Returns a definition of configurable parameters for the sensor and its streams.
        This could be a dictionary describing parameter names, types, default values, etc.
        Example:
        {
            "NMEA0183_SERIAL": {
                "port": {"type": "str", "default": "COM10", "description": "Virtual COM port"},
                "baudrate": {"type": "int", "default": 38400, "options": [9600, 19200, 38400, 115200]},
                "update_rate_hz": {"type": "float", "default": 1.0, "min": 0.1, "max": 10.0}
            },
            "sim_parameters": {
                "latitude_start": {"type": "float", "default": 0.0},
                "longitude_start": {"type": "float", "default": 0.0},
                "movement_pattern": {"type": "str", "default": "static", "options": ["static", "linear_north"]}
            }
        }
        """
        pass

    @abstractmethod
    def configure(self, settings: Dict[str, Any]) -> None:
        """
        Applies new settings to the sensor.
        The structure of 'settings' should align with get_configuration_options().
        """
        pass

    @abstractmethod
    def start_stream(self, stream_id: str, output_config: Dict[str, Any]) -> bool:
        """
        Starts simulating a specific data stream.
        'stream_id' must be one of the strings returned by get_available_data_streams().
        'output_config' contains specifics for the output, e.g., {'port': 'COM10', 'baudrate': 38400}.
        Returns True if successful, False otherwise.
        """
        pass

    @abstractmethod
    def stop_stream(self, stream_id: str) -> bool:
        """
        Stops the simulation for a specific stream.
        Returns True if successful, False otherwise.
        """
        pass

    @abstractmethod
    def is_stream_running(self, stream_id: str) -> bool:
        """Checks if a specific data stream is currently active."""
        pass

    @abstractmethod
    def get_stream_status(self, stream_id: str) -> Dict[str, Any]:
        """
        Returns status information for a specific stream.
        Example: {"running": True, "messages_sent": 1024, "output_target": "COM10"}
        """
        pass

    def get_sensor_health(self) -> Dict[str, Any]:
        """
        Returns general health or status information about the sensor itself (not a specific stream).
        Optional to implement with more detail.
        """
        return {"status": "nominal"}