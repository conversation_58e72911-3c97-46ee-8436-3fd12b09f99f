from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class BaseOutputHandler(ABC):
    """Base class for all output handlers."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.is_connected = False
    
    @abstractmethod
    def open(self) -> bool:
        """Open the output connection/channel."""
        pass
    
    @abstractmethod
    def close(self) -> None:
        """Close the output connection/channel."""
        pass
    
    @abstractmethod
    def send_data(self, data: bytes) -> bool:
        """Send data through the output connection/channel."""
        pass
    
    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        """Get the current status of the output handler."""
        pass