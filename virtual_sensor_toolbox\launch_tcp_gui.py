#!/usr/bin/env python3
"""
Launcher script for the TCP/IP Output Handler GUI
Run this from the virtual_sensor_toolbox directory
"""

import sys
import os

# Ensure we're in the right directory and add to path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def main():
    """Launch the TCP/IP GUI application"""
    try:
        print("Starting TCP/IP Output Handler GUI...")
        
        from gui.tcp_ip_gui import TcpIpGui
        
        # Create and run the GUI
        app = TcpIpGui()
        app.run()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure you're running this from the virtual_sensor_toolbox directory")
        print("2. Ensure all required modules are available")
        print("3. Check that the outputs module is properly configured")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting GUI: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
