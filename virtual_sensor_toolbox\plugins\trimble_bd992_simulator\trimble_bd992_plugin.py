import time
import threading
from typing import List, Dict, Any
from core.virtual_sensor import <PERSON><PERSON>ensor
from outputs.base_output_handler import <PERSON><PERSON>ut<PERSON><PERSON>andler
# We will now import TcpIpOutputHandler instead of SerialOutputHandler
from outputs.tcp_ip_output_handler import TcpIpOutputHandler
# from outputs.serial_output_handler import SerialOutputHandler # Remove or comment out

class TrimbleBD992Simulator(VirtualSensor):
    def __init__(self):
        self.config = {}
        self._active_streams: Dict[str, Dict[str, Any]] = {} # Store thread, stop_event, and handler
        self.default_config = {
            # Removed NMEA0183_SERIAL_PRIMARY, or you can rename it
            "NMEA0183_TCPIP_PRIMARY": { # Renamed for clarity
                "host": "0.0.0.0",
                "tcp_port": 50100,
                "update_rate_hz": 1.0,
            },
            # You can add other TCP/IP streams if needed, e.g., for proprietary data
            # "PROPRIETARY_TCPIP_DATA": {
            #     "host": "0.0.0.0",
            #     "tcp_port": 50101,
            #     "update_rate_hz": 10.0,
            # },
            "simulation_parameters": {
                "start_latitude": 45.0,
                "start_longitude": -75.0,
                "start_altitude": 50.0,
                "movement_model": "static"
            }
        }
        self.configure(self.default_config)

    def get_name(self) -> str:
        return "Trimble BD992 GNSS (TCP/IP)"

    def get_description(self) -> str:
        return "Simulates a Trimble BD992 GNSS receiver, outputting NMEA 0183 data via TCP/IP."

    def get_available_data_streams(self) -> List[str]:
        # Adjust stream names to reflect TCP/IP focus
        return ["NMEA0183_TCPIP_PRIMARY"] # Example: "PROPRIETARY_TCPIP_DATA" could be another

    def get_configuration_options(self) -> Dict[str, Any]:
        return {
            "NMEA0183_TCPIP_PRIMARY": {
                 "host": {"type": "str", "default": "0.0.0.0", "description": "Host IP to listen on (0.0.0.0 for all)"},
                 "tcp_port": {"type": "int", "default": 50100, "description": "TCP Port for NMEA data"},
                 "update_rate_hz": {"type": "float", "default": 1.0, "min": 0.1, "max": 10.0}
            },
            # "PROPRIETARY_TCPIP_DATA": { # Example
            #      "host": {"type": "str", "default": "0.0.0.0"},
            #      "tcp_port": {"type": "int", "default": 50101},
            #      "update_rate_hz": {"type": "float", "default": 10.0}
            # },
            "simulation_parameters": { # This part remains the same
                "start_latitude": {"type": "float", "default": 45.0},
                "start_longitude": {"type": "float", "default": -75.0},
                "start_altitude": {"type": "float", "default": 50.0},
                "movement_model": {"type": "str", "default": "static", "options": ["static", "linear_north_0.1mps"]}
            }
        }

    def configure(self, settings: Dict[str, Any]) -> None:
        # (configure method remains the same - ensure it handles nested dicts for updates)
        for key, value in settings.items():
            if isinstance(value, dict) and key in self.config and isinstance(self.config[key], dict):
                self.config[key].update(value)
            else:
                self.config[key] = value
        print(f"[{self.get_name()}] Configured with: {self.config}")

    def _generate_gpgga(self, current_lat, current_lon, current_alt) -> str:
        # (_generate_gpgga method remains the same)
        timestamp = time.strftime("%H%M%S.00", time.gmtime())
        lat_deg = int(abs(current_lat))
        lat_min = (abs(current_lat) - lat_deg) * 60
        lat_str = f"{lat_deg:02d}{lat_min:07.4f}".replace('.', '')
        lon_deg = int(abs(current_lon))
        lon_min = (abs(current_lon) - lon_deg) * 60
        lon_str = f"{lon_deg:03d}{lon_min:07.4f}".replace('.', '')
        lat_hemi = 'N' if current_lat >= 0 else 'S'
        lon_hemi = 'E' if current_lon >= 0 else 'W'
        nmea_sentence_body = f"GPGGA,{timestamp},{lat_str},{lat_hemi},{lon_str},{lon_hemi},1,08,1.0,{current_alt:.1f},M,0.0,M,,"
        checksum = 0
        for char in nmea_sentence_body:
            checksum ^= ord(char)
        return f"${nmea_sentence_body}*{checksum:02X}\r\n"

    def _simulation_loop(self, stream_id: str, stop_event: threading.Event, output_handler: BaseOutputHandler):
        # (_simulation_loop method logic remains largely the same,
        # it's abstracted by BaseOutputHandler)
        stream_settings = self.config.get(stream_id, {})
        sim_params = self.config.get("simulation_parameters", {})

        update_rate_hz = stream_settings.get("update_rate_hz", 1.0)
        delay = 1.0 / update_rate_hz if update_rate_hz > 0 else 1.0

        current_lat = sim_params.get("start_latitude", 45.0)
        current_lon = sim_params.get("start_longitude", -75.0)
        current_alt = sim_params.get("start_altitude", 50.0)
        movement_model = sim_params.get("movement_model", "static")

        print(f"[{self.get_name()}] Starting simulation loop for '{stream_id}' at {update_rate_hz} Hz. Output handler: {type(output_handler).__name__}")
        
        if not output_handler.open(): # This might block for TCP until a client connects or timeout
            print(f"[{self.get_name()}] Output handler for '{stream_id}' failed to open or no client connected initially. Simulation may not send data until connection is established.")
            # Depending on handler.open() behavior, decide if loop should proceed
            # If open() returns false on timeout without client, the loop won't start sending.
            # The send_data() method in TcpIpOutputHandler will attempt to accept new clients.
        
        client_ever_connected = output_handler.is_connected

        try:
            while not stop_event.is_set():
                if not output_handler.is_connected and self.config.get(stream_id, {}).get("require_client_to_generate", False):
                    # Optional: if you want to pause data generation when no client is connected
                    time.sleep(0.5) # Check for client periodically
                    output_handler.send_data(b'') # This call can trigger a new client accept attempt in our TCP handler
                    if output_handler.is_connected and not client_ever_connected:
                        client_ever_connected = True # Log first connection
                        print(f"[{self.get_name()}/{stream_id}] Client connected. Resuming data generation.")
                    elif not output_handler.is_connected and client_ever_connected:
                        client_ever_connected = False # Log disconnection
                        print(f"[{self.get_name()}/{stream_id}] Client disconnected. Pausing active data generation. Will resume if client reconnects.")
                    continue # Skip data generation if no client and configured to require one
                elif output_handler.is_connected and not client_ever_connected : # first time client connected
                    client_ever_connected = True # Log first connection
                    print(f"[{self.get_name()}/{stream_id}] Client connected. Starting data generation.")


                gpgga_sentence = self._generate_gpgga(current_lat, current_lon, current_alt)
                
                data_sent = output_handler.send_data(gpgga_sentence.encode('ascii'))
                # if data_sent:
                # print(f"DEBUG: Sent: {gpgga_sentence.strip()}") # Console debugging for actual sends

                if movement_model == "linear_north_0.1mps":
                    current_lat += (0.1 / 111000.0) * delay 
                
                time.sleep(delay)
        finally:
            print(f"[{self.get_name()}] Exiting simulation loop for '{stream_id}'.")
            output_handler.close()
        
        print(f"[{self.get_name()}] Stopped data stream '{stream_id}'.")


    def start_stream(self, stream_id: str, output_config_override: Dict[str, Any] = None) -> bool:
        if stream_id not in self.get_available_data_streams():
            print(f"[{self.get_name()}] Unknown stream_id: {stream_id}")
            return False
        if self.is_stream_running(stream_id):
            print(f"[{self.get_name()}] Stream '{stream_id}' is already running.")
            return False

        stream_settings = self.config.get(stream_id, {}).copy()
        if output_config_override:
            stream_settings.update(output_config_override)

        output_handler: BaseOutputHandler = None

        # Only use TcpIpOutputHandler now
        if "TCPIP" in stream_id.upper():
            if "tcp_port" not in stream_settings: # host can default in handler
                print(f"[{self.get_name()}] Error: 'tcp_port' not specified for TCP/IP stream '{stream_id}'.")
                return False
            output_handler = TcpIpOutputHandler(stream_settings)
        else:
            print(f"[{self.get_name()}] Stream type of '{stream_id}' not supported. Only TCPIP streams are handled.")
            return False
        
        stop_event = threading.Event()
        thread = threading.Thread(target=self._simulation_loop, args=(stream_id, stop_event, output_handler))
        thread.daemon = True
        
        self._active_streams[stream_id] = {
            "thread": thread,
            "stop_event": stop_event,
            "handler": output_handler
        }
        
        try:
            thread.start()
            print(f"[{self.get_name()}] Successfully initiated stream '{stream_id}'. Handler should be listening/connecting.")
            return True
        except Exception as e:
            print(f"[{self.get_name()}] Failed to start thread for stream '{stream_id}': {e}")
            if stream_id in self._active_streams: # Clean up
                if self._active_streams[stream_id].get("handler"):
                    self._active_streams[stream_id]["handler"].close()
                del self._active_streams[stream_id]
            return False

    # stop_stream, is_stream_running, get_stream_status methods remain the same
    def stop_stream(self, stream_id: str) -> bool:
        if not self.is_stream_running(stream_id):
            # print(f"[{self.get_name()}] Stream '{stream_id}' is not running.") # Can be noisy if called often
            return False
        
        stream_info = self._active_streams.get(stream_id)
        if stream_info:
            print(f"[{self.get_name()}] Attempting to stop stream: {stream_id}")
            stream_info["stop_event"].set()
            # The handler.close() is called in the simulation_loop's finally block.
            # However, if the handler's open() method blocked and the thread never really ran the loop,
            # we might need to explicitly close the handler here too.
            # The TcpIpOutputHandler's close is idempotent.
            if stream_info.get("handler"):
                stream_info["handler"].close()

            stream_info["thread"].join(timeout=2) # Reduced timeout
            
            if stream_info["thread"].is_alive():
                 print(f"[{self.get_name()}] Warning: Stream '{stream_id}' thread did not terminate gracefully.")
            # else:
            # print(f"[{self.get_name()}] Stream '{stream_id}' thread terminated.")
            
            del self._active_streams[stream_id]
            # print(f"[{self.get_name()}] Stream '{stream_id}' resources cleaned up.") # Can be noisy
            return True
        return False

    def is_stream_running(self, stream_id: str) -> bool:
        stream_info = self._active_streams.get(stream_id)
        if stream_info and stream_info["thread"].is_alive():
            return True
        # If thread is not alive but entry still exists, it means it stopped uncleanly or is being stopped
        # Clean up if thread died without proper stop_stream call
        elif stream_id in self._active_streams and stream_info and not stream_info["thread"].is_alive():
            print(f"[{self.get_name()}] Cleaning up dead thread for stream '{stream_id}'.")
            if stream_info.get("handler"):
                 stream_info["handler"].close()
            del self._active_streams[stream_id]
        return False

    def get_stream_status(self, stream_id: str) -> Dict[str, Any]:
        if stream_id in self._active_streams: # Check presence before assuming running
            stream_info = self._active_streams[stream_id]
            handler_status = stream_info["handler"].get_status() if stream_info.get("handler") else {"status": "unknown_handler"}
            return {
                "running": stream_info["thread"].is_alive(), # More accurate state of thread
                "output_type": handler_status.get("type", "unknown"),
                "output_details": handler_status,
            }
        return {"running": False, "output_details": {"status": "not_active"}}