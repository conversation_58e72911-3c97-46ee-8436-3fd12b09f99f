"""
Dark theme styling constants for the GUI application
"""

# Color palette for dark theme
COLORS = {
    # Background colors
    'bg_primary': '#1e1e1e',      # Main background
    'bg_secondary': '#2d2d2d',    # Secondary panels
    'bg_tertiary': '#3c3c3c',     # Input fields, buttons
    'bg_hover': '#4a4a4a',        # Hover states
    
    # Text colors
    'text_primary': '#ffffff',     # Main text
    'text_secondary': '#b3b3b3',   # Secondary text
    'text_muted': '#808080',       # Muted text
    
    # Accent colors
    'accent_blue': '#007acc',      # Primary accent
    'accent_green': '#4caf50',     # Success/connected
    'accent_red': '#f44336',       # Error/disconnected
    'accent_orange': '#ff9800',    # Warning
    'accent_purple': '#9c27b0',    # Info
    
    # Border colors
    'border_primary': '#555555',   # Main borders
    'border_secondary': '#404040', # Secondary borders
    'border_focus': '#007acc',     # Focus states
}

# Font settings
FONTS = {
    'default': ('Segoe UI', 10),
    'heading': ('Segoe UI', 12, 'bold'),
    'subheading': ('Segoe UI', 11, 'bold'),
    'small': ('Segoe UI', 9),
    'monospace': ('Consolas', 10),
}

# Widget styles
STYLES = {
    'frame': {
        'bg': COLORS['bg_primary'],
        'relief': 'flat',
        'bd': 0,
    },
    'panel_frame': {
        'bg': COLORS['bg_secondary'],
        'relief': 'solid',
        'bd': 1,
        'highlightbackground': COLORS['border_primary'],
    },
    'label': {
        'bg': COLORS['bg_primary'],
        'fg': COLORS['text_primary'],
        'font': FONTS['default'],
    },
    'heading_label': {
        'bg': COLORS['bg_primary'],
        'fg': COLORS['text_primary'],
        'font': FONTS['heading'],
    },
    'subheading': {
        'bg': COLORS['bg_primary'],
        'fg': COLORS['text_primary'],
        'font': FONTS['subheading'],
    },
    'entry': {
        'bg': COLORS['bg_tertiary'],
        'fg': COLORS['text_primary'],
        'font': FONTS['default'],
        'relief': 'solid',
        'bd': 1,
        'highlightbackground': COLORS['border_primary'],
        'highlightcolor': COLORS['border_focus'],
        'insertbackground': COLORS['text_primary'],
    },
    'button': {
        'bg': COLORS['bg_tertiary'],
        'fg': COLORS['text_primary'],
        'font': FONTS['default'],
        'relief': 'solid',
        'bd': 1,
        'highlightbackground': COLORS['border_primary'],
        'activebackground': COLORS['bg_hover'],
        'activeforeground': COLORS['text_primary'],
    },
    'button_primary': {
        'bg': COLORS['accent_blue'],
        'fg': COLORS['text_primary'],
        'font': FONTS['default'],
        'relief': 'solid',
        'bd': 1,
        'highlightbackground': COLORS['accent_blue'],
        'activebackground': '#005a9e',
        'activeforeground': COLORS['text_primary'],
    },
    'button_success': {
        'bg': COLORS['accent_green'],
        'fg': COLORS['text_primary'],
        'font': FONTS['default'],
        'relief': 'solid',
        'bd': 1,
        'highlightbackground': COLORS['accent_green'],
        'activebackground': '#388e3c',
        'activeforeground': COLORS['text_primary'],
    },
    'button_danger': {
        'bg': COLORS['accent_red'],
        'fg': COLORS['text_primary'],
        'font': FONTS['default'],
        'relief': 'solid',
        'bd': 1,
        'highlightbackground': COLORS['accent_red'],
        'activebackground': '#d32f2f',
        'activeforeground': COLORS['text_primary'],
    },
    'text': {
        'bg': COLORS['bg_tertiary'],
        'fg': COLORS['text_primary'],
        'font': FONTS['monospace'],
        'relief': 'solid',
        'bd': 1,
        'highlightbackground': COLORS['border_primary'],
        'insertbackground': COLORS['text_primary'],
    },
    'scrollbar': {
        'bg': COLORS['bg_secondary'],
        'troughcolor': COLORS['bg_primary'],
        'activebackground': COLORS['bg_hover'],
        'highlightbackground': COLORS['bg_secondary'],
    },
}

# Status indicator colors
STATUS_COLORS = {
    'connected': COLORS['accent_green'],
    'disconnected': COLORS['accent_red'],
    'listening': COLORS['accent_orange'],
    'idle': COLORS['text_muted'],
}

# Layout constants
LAYOUT = {
    'padding': 10,
    'small_padding': 5,
    'large_padding': 15,
    'button_width': 12,
    'entry_width': 20,
    'status_indicator_size': 12,
}
