import sys
import os
import time
import traceback  # Add this for better error reporting

# --- Start of Diagnostic Code ---
print("--- Initial sys.path ---")
for p in sys.path:
    print(p)
print("-------------------------")

project_root = os.path.dirname(os.path.abspath(__file__))
print(f"Calculated project_root: {project_root}")

# Check if project_root is already in sys.path, to avoid duplicate if script is run multiple times in some contexts
if project_root not in sys.path:
    sys.path.insert(0, project_root)
    print(f"Added to sys.path: {project_root}")
else:
    print(f"Project root {project_root} was already in sys.path.")

print("--- sys.path After insert ---")
for p in sys.path:
    print(p)
print("----------------------------")

# List all files in the plugins directory to verify structure
plugins_dir = os.path.join(project_root, 'plugins')
print(f"\n--- Listing files in plugins directory: {plugins_dir} ---")
if os.path.exists(plugins_dir):
    for root, dirs, files in os.walk(plugins_dir):
        for file in files:
            print(os.path.join(root, file))
else:
    print(f"Plugins directory not found at: {plugins_dir}")
print("----------------------------")

# The rest of your main.py starts here
try:
    print("\nAttempting to import TrimbleBD992Simulator...")
    # Use the correct path based on your file structure
    from plugins.trimble_bd992_simulator.trimble_bd992_plugin import TrimbleBD992Simulator
    print("Import successful!")
except ImportError as e:
    print(f"Import error: {e}")
    print("Traceback:")
    traceback.print_exc()
    
    # Try alternative import paths - adjust these based on your actual file structure
    print("\nTrying alternative import paths...")
    try:
        # If the file is directly in the plugins directory
        from plugins.trimble_bd992_simulator.trimble_bd992_plugin import TrimbleBD992Simulator
        print("Alternative import successful!")
    except ImportError as e:
        print(f"Alternative import also failed: {e}")
        sys.exit(1)  # Exit if we can't import the required module

def main():
    try:
        print("Virtual Sensor Toolbox - TCP/IP Output Test")

        # --- INFO ---
        print("\n--- TCP/IP SERVER INFO ---")
        print("The simulator will start a TCP/IP server for the NMEA data stream.")
        print("You'll need a TCP client (e.g., netcat, telnet, PuTTY in 'raw' or 'telnet' mode,")
        print("or your own test application) to connect to the specified host and port.")
        print("-------------------------\n")

        available_sensor_classes = {
            "TrimbleBD992_TCP": TrimbleBD992Simulator
        }
        print(f"Creating sensor instance...")
        sensor_instance = available_sensor_classes["TrimbleBD992_TCP"]()
        print(f"Sensor instance created successfully.")

        print(f"Sensor Name: {sensor_instance.get_name()}")
        
        # --- Configure Sensor (Example: Set target TCP host/port and simulation details) ---
        TARGET_TCP_HOST = "127.0.0.1"  # Or "0.0.0.0" to listen on all interfaces
        TARGET_TCP_PORT = 50100        # The port the simulator will listen on

        sensor_config_overrides = {
            "NMEA0183_TCPIP_PRIMARY": { # Matches the stream ID in the plugin
                "host": TARGET_TCP_HOST,
                "tcp_port": TARGET_TCP_PORT,
                "update_rate_hz": 1.0
            },
            "simulation_parameters": {
                "start_latitude": 34.0522, # Los Angeles
                "start_longitude": -118.2437,
                "start_altitude": 70.0,
                "movement_model": "static" #"linear_north_0.1mps"
            },
            # Optional: Configure the simulation loop to wait for a client before heavy generation
            # "NMEA0183_TCPIP_PRIMARY": {  # Merge this with the above if using
            #     "require_client_to_generate": True 
            # }
        }
        sensor_instance.configure(sensor_config_overrides)
        
        # --- Start the TCP/IP Stream ---
        primary_stream_id = "NMEA0183_TCPIP_PRIMARY" # Must match available_data_streams()
        print(f"\nAttempting to start TCP/IP stream: {primary_stream_id} on {TARGET_TCP_HOST}:{TARGET_TCP_PORT}")

        if sensor_instance.start_stream(primary_stream_id):
            print(f"Stream '{primary_stream_id}' initiated. TCP Server should be listening on {TARGET_TCP_HOST}:{TARGET_TCP_PORT}.")
            print(f"Connect your TCP client now to receive NMEA data.")
            
            try:
                # Let it run, checking status periodically
                for i in range(30): # Run for 30 seconds or until Ctrl+C
                    status = sensor_instance.get_stream_status(primary_stream_id)
                    # print(f"Status ({i+1}/30s): Running: {status.get('running')}, Client: {status.get('output_details', {}).get('client_connected', 'N/A')}, Addr: {status.get('output_details', {}).get('client_address', 'N/A')}")
                    # More detailed print:
                    running_status = status.get('running', False)
                    output_details = status.get('output_details', {})
                    client_connected = output_details.get('client_connected', 'N/A')
                    client_addr = output_details.get('client_address', 'N/A')
                    print(f"Status ({i+1}/30s): ThreadRunning: {running_status}, ClientConnected: {client_connected}, ClientAddr: {client_addr}")
                    if not running_status and i > 1: # if thread died unexpectedly
                        print("Stream thread seems to have stopped. Exiting loop.")
                        break
                    time.sleep(1)
                print("Test duration finished.")
            except KeyboardInterrupt:
                print("\nCtrl+C detected. Stopping simulator...")
            finally:
                print(f"\nAttempting to stop stream: {primary_stream_id}")
                sensor_instance.stop_stream(primary_stream_id) # This should also close the handler
                # Wait a moment for cleanup
                time.sleep(0.5)
                final_status = sensor_instance.get_stream_status(primary_stream_id)
                print(f"Is stream '{primary_stream_id}' active after stop? {final_status.get('running')}")
                print(f"Final client status: {final_status.get('output_details', {}).get('client_connected')}")


        else:
            print(f"Failed to start stream: {primary_stream_id}. Check if port {TARGET_TCP_PORT} is already in use or host is invalid.")

        print("\nTCP/IP Output Test Finished.")
    except Exception as e:
        print(f"Error in main function: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
