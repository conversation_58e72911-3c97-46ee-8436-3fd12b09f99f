#!/usr/bin/env python3
"""
Standalone entry point for the TCP/IP Output Handler GUI
"""

import sys
import os

# Add the project root to the path for imports
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def main():
    """Main entry point for the GUI application"""
    try:
        from gui.tcp_ip_gui import TcpIpGui
        
        # Create and run the GUI
        app = TcpIpGui()
        app.run()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure you're running this from the correct directory and all dependencies are available.")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting GUI: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
