# TCP/IP Output Handler GUI

A modern, dark-themed GUI application for managing the TCP/IP output functionality of the Virtual Sensor Toolbox.

## Features

### 🎨 Modern Dark Theme
- Clean, minimal design with dark color scheme
- Professional appearance with proper visual hierarchy
- Responsive layout that adapts to window resizing

### 🔧 Server Configuration
- **Host/IP Address**: Configure the server listening address (default: 0.0.0.0 for all interfaces)
- **Port**: Set the TCP port number (default: 50100)
- **Validation**: Real-time validation of configuration parameters

### 🎛️ Server Control
- **Start Server**: Begin listening for client connections
- **Stop Server**: Gracefully shutdown the server
- **Real-time Status**: Live monitoring of server and client states

### 📊 Status Monitoring
- **Server Status**: Visual indicator showing if server is listening
- **Client Status**: Shows when clients are connected/disconnected
- **Client Address**: Displays the IP address of connected clients
- **Data Counter**: Tracks the number of data packets sent

### 📡 Data Transmission
- **Test Data Input**: Send custom NMEA or other data to connected clients
- **Pre-filled Example**: Includes sample NMEA GPGGA sentence
- **Automatic Formatting**: Adds proper line endings (CRLF) if missing

### 📝 Activity Log
- **Real-time Logging**: All server activities are logged with timestamps
- **Log Levels**: INFO, WARNING, ERROR message categorization
- **Auto-scroll**: Automatically scrolls to show latest messages
- **Log Management**: Clear log functionality and automatic size limiting

## Usage

### Starting the GUI

From the `virtual_sensor_toolbox` directory, run:

```bash
python launch_tcp_gui.py
```

Or alternatively:

```bash
python gui/tcp_ip_gui_main.py
```

### Basic Workflow

1. **Configure Server**:
   - Set the desired host/IP address (0.0.0.0 for all interfaces, 127.0.0.1 for localhost only)
   - Set the port number (must be between 1-65535)

2. **Start Server**:
   - Click "Start Server" to begin listening for connections
   - The server status will show "Listening" when active

3. **Connect Client**:
   - Use any TCP client (telnet, netcat, PuTTY, custom application)
   - Connect to the configured host and port
   - Client status will show "Connected" when a client connects

4. **Send Data**:
   - Enter test data in the "Test Data" field
   - Click "Send Test Data" to transmit to connected clients
   - Monitor the activity log for transmission status

5. **Monitor Activity**:
   - Watch the status indicators for real-time connection state
   - Review the activity log for detailed information
   - Track data transmission statistics

### Example Client Connection

Using telnet to connect to the server:

```bash
telnet 127.0.0.1 50100
```

Using netcat:

```bash
nc 127.0.0.1 50100
```

## Technical Details

### Architecture
- Built with Python's tkinter for maximum compatibility
- Threaded design prevents GUI blocking during network operations
- Real-time status monitoring with 1-second update intervals
- Thread-safe communication between GUI and network components

### Network Protocol
- TCP/IP server implementation
- Supports one client connection at a time
- Automatic client reconnection handling
- Graceful connection cleanup on shutdown

### Error Handling
- Comprehensive error catching and logging
- User-friendly error messages
- Graceful degradation on network failures
- Automatic resource cleanup

## Customization

The GUI appearance can be customized by modifying `styles.py`:

- **Colors**: Update the `COLORS` dictionary for different color schemes
- **Fonts**: Modify the `FONTS` dictionary for different typography
- **Layout**: Adjust the `LAYOUT` constants for spacing and sizing

## Integration

The GUI integrates seamlessly with the existing `TcpIpOutputHandler` class without modifying its core functionality. It can be used alongside or instead of the command-line interface for the Virtual Sensor Toolbox.

## Requirements

- Python 3.6+
- tkinter (included with most Python installations)
- Access to the Virtual Sensor Toolbox modules

No additional dependencies required!
