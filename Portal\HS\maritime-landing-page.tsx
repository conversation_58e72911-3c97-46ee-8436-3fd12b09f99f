import React, { useState } from 'react';
import { 
  Ship, Database, Settings, Search, 
  User, Bell, ChevronRight, Filter,
  Info, Download, Upload, Clipboard,
  Wrench, BarChart2, Menu, X
} from 'lucide-react';

const MaritimeLandingPage = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  
  // Vessel categories
  const categories = [
    { id: 'all', name: 'All Vessels' },
    { id: 'autonomous', name: 'Autonomous Vessels' },
    { id: 'survey', name: 'Survey Vessels' },
    { id: 'construction', name: 'Construction Vessels' },
    { id: 'psv', name: 'PSVs' }
  ];
  
  // Vessel data based on the provided images
  const vessels = [
    {
      id: 'vessel-1',
      name: 'USV Surveyor',
      category: 'autonomous',
      image: '/api/placeholder/640/320',
      description: 'Autonomous Survey Vessel with dual radar domes and advanced positioning systems',
      specs: {
        length: '12m',
        beam: '5m',
        draft: '1.5m',
        speed: '8 knots',
        endurance: '48 hours',
        status: 'Active'
      }
    },
    {
      id: 'vessel-2',
      name: 'USV Explorer',
      category: 'autonomous',
      image: '/api/placeholder/640/320',
      description: 'Twin-hull autonomous vessel with advanced sensor package for hydrographic surveys',
      specs: {
        length: '9m',
        beam: '4.5m',
        draft: '1.2m',
        speed: '10 knots',
        endurance: '36 hours',
        status: 'Active'
      }
    },
    {
      id: 'vessel-3',
      name: 'Ocean Constructor',
      category: 'construction',
      image: '/api/placeholder/640/320',
      description: 'Specialized vessel for offshore construction with integrated work platform',
      specs: {
        length: '78m',
        beam: '22m',
        draft: '5.8m',
        deck_area: '820m²',
        accommodation: '60 persons',
        status: 'On Charter'
      }
    },
    {
      id: 'vessel-4',
      name: 'Armada 15',
      category: 'autonomous',
      image: '/api/placeholder/640/320',
      description: 'Green semi-submersible autonomous vessel for specialized survey operations',
      specs: {
        length: '8m',
        beam: '3m',
        draft: '1.8m',
        speed: '6 knots',
        endurance: '72 hours',
        status: 'In Port'
      }
    },
    {
      id: 'vessel-5',
      name: 'AUV Explorer',
      category: 'autonomous',
      image: '/api/placeholder/640/320',
      description: 'Autonomous underwater vehicle with bright green hull for high visibility operations',
      specs: {
        length: '5m',
        beam: '1.5m',
        max_depth: '300m',
        endurance: '24 hours',
        sensors: 'Multibeam, SSS, SBP',
        status: 'On Trials'
      }
    },
    {
      id: 'vessel-6',
      name: 'Offshore Surveyor',
      category: 'survey',
      image: '/api/placeholder/640/320',
      description: 'Multi-purpose survey vessel with integrated survey systems and dynamic positioning',
      specs: {
        length: '65m',
        beam: '16m',
        draft: '5.2m',
        speed: '14 knots',
        accommodation: '40 persons',
        status: 'Active'
      }
    }
  ];
  
  // Filter vessels based on active category and search term
  const filteredVessels = vessels.filter(vessel => 
    (activeCategory === 'all' || vessel.category === activeCategory) &&
    (vessel.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
     vessel.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );
  
  // Get status color
  const getStatusColor = (status) => {
    switch(status) {
      case 'Active': return 'bg-green-500';
      case 'On Charter': return 'bg-blue-500';
      case 'In Port': return 'bg-yellow-500';
      case 'On Trials': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-gray-900 text-white shadow-md">
        <div className="container mx-auto px-4">
          {/* Top navigation */}
          <div className="flex items-center justify-between py-3">
            <div className="flex items-center">
              <Ship className="text-green-400 mr-2" size={28} />
              <span className="text-xl font-semibold">OpenBridge Fleet</span>
            </div>
            
            <div className="hidden md:flex items-center space-x-8">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search vessels, projects..."
                  className="bg-gray-800 text-white pl-10 pr-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-400 w-64"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
              </div>
              
              <nav className="flex items-center space-x-6">
                <a href="#" className="text-gray-300 hover:text-white">Dashboard</a>
                <a href="#" className="text-gray-300 hover:text-white">Projects</a>
                <a href="#" className="text-gray-300 hover:text-white">Reports</a>
              </nav>
            </div>
            
            <div className="flex items-center space-x-4">
              <button className="p-2 rounded-full hover:bg-gray-800">
                <Bell size={20} />
              </button>
              <button className="p-2 rounded-full hover:bg-gray-800">
                <Settings size={20} />
              </button>
              <div className="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center">
                <User size={18} />
              </div>
              <button 
                className="md:hidden p-2 rounded-full hover:bg-gray-800"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>
          
          {/* Mobile menu */}
          {mobileMenuOpen && (
            <div className="md:hidden py-3 border-t border-gray-800">
              <div className="relative mb-3">
                <input
                  type="text"
                  placeholder="Search vessels, projects..."
                  className="w-full bg-gray-800 text-white pl-10 pr-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-400"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
              </div>
              
              <nav className="flex flex-col space-y-2">
                <a href="#" className="text-gray-300 hover:text-white py-1">Dashboard</a>
                <a href="#" className="text-gray-300 hover:text-white py-1">Projects</a>
                <a href="#" className="text-gray-300 hover:text-white py-1">Reports</a>
              </nav>
            </div>
          )}
          
          {/* Category navigation */}
          <div className="py-2 border-t border-gray-800 overflow-x-auto">
            <div className="flex space-x-2">
              {categories.map(category => (
                <button
                  key={category.id}
                  className={`px-4 py-2 rounded-md whitespace-nowrap transition-colors ${
                    activeCategory === category.id 
                      ? 'bg-green-500 text-white' 
                      : 'text-gray-300 hover:bg-gray-800'
                  }`}
                  onClick={() => setActiveCategory(category.id)}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      </header>
      
      {/* Main content */}
      <main className="flex-grow container mx-auto px-4 py-6">
        {/* Page title */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800">
            {activeCategory === 'all' ? 'All Vessels' : categories.find(c => c.id === activeCategory)?.name}
          </h1>
          <p className="text-gray-600">
            Select a vessel to manage operations and view details
          </p>
        </div>
        
        {/* Vessel grid */}
        {filteredVessels.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredVessels.map(vessel => (
              <div
                key={vessel.id}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 cursor-pointer"
              >
                <div className="relative h-56">
                  <img 
                    src={vessel.image} 
                    alt={vessel.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-0 left-0 right-0 p-4 bg-gradient-to-b from-black/70 to-transparent">
                    <div className="flex justify-between items-center">
                      <h2 className="text-white font-semibold text-lg">{vessel.name}</h2>
                      <div className={`h-3 w-3 rounded-full ${getStatusColor(vessel.specs.status)}`}></div>
                    </div>
                  </div>
                  <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent">
                    <span className="text-white text-sm">{vessel.description}</span>
                  </div>
                </div>
                
                <div className="p-4">
                  <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                    {Object.entries(vessel.specs).map(([key, value]) => 
                      key !== 'status' && (
                        <div key={key}>
                          <span className="text-gray-500 capitalize">{key.replace('_', ' ')}</span>
                          <p className="font-medium">{value}</p>
                        </div>
                      )
                    )}
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <span className={`inline-block h-2 w-2 rounded-full ${getStatusColor(vessel.specs.status)} mr-2`}></span>
                      <span className="text-sm text-gray-600">{vessel.specs.status}</span>
                    </div>
                    <button className="bg-green-500 hover:bg-green-600 text-white text-sm px-4 py-2 rounded-md transition-colors">
                      Select
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <Ship size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-medium text-gray-800 mb-2">No vessels found</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </main>
      
      {/* Footer */}
      <footer className="bg-gray-900 text-gray-400 py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <div className="flex items-center">
                <Ship className="text-green-400 mr-2" size={20} />
                <span className="text-white font-semibold">OpenBridge Fleet Management</span>
              </div>
              <p className="text-sm mt-1">Advanced maritime operations management</p>
            </div>
            
            <div className="flex space-x-6">
              <a href="#" className="text-gray-400 hover:text-white">Help Center</a>
              <a href="#" className="text-gray-400 hover:text-white">Documentation</a>
              <a href="#" className="text-gray-400 hover:text-white">Contact Support</a>
            </div>
          </div>
          <div className="mt-6 pt-6 border-t border-gray-800 text-center text-sm">
            <p>© 2025 OpenBridge. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default MaritimeLandingPage;