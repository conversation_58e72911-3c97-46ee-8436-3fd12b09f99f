graph LR
    subgraph Vessel_Alpha["Survey Vessel Alpha (Edge)"]
        direction LR
        SENSORS[Hydrographic Sensors <br/>(MBES, INS, GNSS, SVS, etc.)] --> MOXA[Moxa NPorts]
        SENSORS_ETH[Ethernet Sensors <br/>(Cameras, HiPAP)] --> ECU[Edge Compute Unit <br/>(Ignition Edge, Local AI, FileCatalyst Agent)]
        MOXA --> ECU
        PTP_V[PTP Time Server] --> ECU
        ECU -- Sparkplug B / Files --> COMMS_V[Comms Array <br/>(Starlink, VSAT)]
        AUV_Dock[AUV/ROV Dock] <--> ECU
    end

    subgraph Shore_Control_Center["Shore Control Center"]
        direction LR
        MQTT_B[MQTT Broker Cluster] --> IG_C[Ignition Gateway Cluster <br/>(Perspective, Historian, AI Services)]
        IG_C <--> DB_C[PostgreSQL DB Cluster <br/>(Historian, AppData, GIS)]
        IG_C --> OPS_W[Operator Workstations <br/>(<PERSON> Browser)]
        FC_S[FileCatalyst Server] <--> RAW_STORE[Raw Data Storage]
        ECU_SHORE_AI[AI/LLM Servers] <--> IG_C
        FIREWALL[Firewall] --> MQTT_B
        FIREWALL --> FC_S
    end

    COMMS_V -- MQTT/FileTransfer --> FIREWALL

    style SENSORS fill:#lightgrey,stroke:#333,stroke-width:2px
    style ECU fill:#lightblue,stroke:#333,stroke-width:2px
    style IG_C fill:#lightgreen,stroke:#333,stroke-width:2px
    style MQTT_B fill:#orange,stroke:#333,stroke-width:2px