"""
Modern dark-themed GUI for TCP/IP Output Handler
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from typing import Dict, Any, Optional
import sys
import os

# Add the project root to the path for imports
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from outputs.tcp_ip_output_handler import TcpIpOutputHandler
from .styles import COLORS, FONTS, STYLES, STATUS_COLORS, LAYOUT


class StatusIndicator(tk.Canvas):
    """Custom status indicator widget"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, width=LAYOUT['status_indicator_size'], 
                        height=LAYOUT['status_indicator_size'], 
                        bg=COLORS['bg_primary'], highlightthickness=0, **kwargs)
        self.status = 'idle'
        self.draw_indicator()
    
    def set_status(self, status: str):
        """Set the status and update the indicator"""
        self.status = status
        self.draw_indicator()
    
    def draw_indicator(self):
        """Draw the status indicator circle"""
        self.delete("all")
        color = STATUS_COLORS.get(self.status, STATUS_COLORS['idle'])
        size = LAYOUT['status_indicator_size']
        self.create_oval(2, 2, size-2, size-2, fill=color, outline=color)


class TcpIpGui:
    """Main GUI application for TCP/IP Output Handler"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.handler: Optional[TcpIpOutputHandler] = None
        self.status_update_thread: Optional[threading.Thread] = None
        self.running = False
        self.data_sent_count = 0
        
        # Configuration variables
        self.host_var = tk.StringVar(value="0.0.0.0")
        self.port_var = tk.StringVar(value="50100")
        
        self.setup_window()
        self.create_widgets()
        self.setup_styles()
        
    def setup_window(self):
        """Configure the main window"""
        self.root.title("TCP/IP Output Handler - Virtual Sensor Toolbox")
        self.root.geometry("800x600")
        self.root.configure(bg=COLORS['bg_primary'])
        self.root.resizable(True, True)
        
        # Set minimum size
        self.root.minsize(600, 400)
        
        # Configure grid weights for responsive layout
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
    
    def create_widgets(self):
        """Create and layout all GUI widgets"""
        # Main container
        main_frame = tk.Frame(self.root, **STYLES['frame'])
        main_frame.grid(row=0, column=0, sticky="nsew", padx=LAYOUT['padding'], pady=LAYOUT['padding'])
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Title
        title_label = tk.Label(main_frame, text="TCP/IP Output Handler", **STYLES['heading_label'])
        title_label.grid(row=0, column=0, sticky="w", pady=(0, LAYOUT['large_padding']))
        
        # Content area
        content_frame = tk.Frame(main_frame, **STYLES['frame'])
        content_frame.grid(row=1, column=0, sticky="nsew")
        content_frame.grid_rowconfigure(1, weight=1)
        content_frame.grid_columnconfigure(0, weight=1)
        content_frame.grid_columnconfigure(1, weight=1)
        
        # Left panel - Configuration and Controls
        self.create_left_panel(content_frame)
        
        # Right panel - Status and Logs
        self.create_right_panel(content_frame)
    
    def create_left_panel(self, parent):
        """Create the left panel with configuration and controls"""
        left_frame = tk.Frame(parent, **STYLES['panel_frame'])
        left_frame.grid(row=0, column=0, sticky="nsew", padx=(0, LAYOUT['small_padding']))
        left_frame.grid_rowconfigure(2, weight=1)
        
        # Configuration section
        config_label = tk.Label(left_frame, text="Server Configuration", **STYLES['subheading'])
        config_label.grid(row=0, column=0, columnspan=2, sticky="w", 
                         padx=LAYOUT['padding'], pady=(LAYOUT['padding'], LAYOUT['small_padding']))
        
        config_frame = tk.Frame(left_frame, **STYLES['frame'])
        config_frame.grid(row=1, column=0, columnspan=2, sticky="ew", 
                         padx=LAYOUT['padding'], pady=LAYOUT['small_padding'])
        
        # Host input
        tk.Label(config_frame, text="Host/IP:", **STYLES['label']).grid(row=0, column=0, sticky="w", pady=LAYOUT['small_padding'])
        self.host_entry = tk.Entry(config_frame, textvariable=self.host_var, 
                                  width=LAYOUT['entry_width'], **STYLES['entry'])
        self.host_entry.grid(row=0, column=1, sticky="ew", padx=(LAYOUT['small_padding'], 0), pady=LAYOUT['small_padding'])
        
        # Port input
        tk.Label(config_frame, text="Port:", **STYLES['label']).grid(row=1, column=0, sticky="w", pady=LAYOUT['small_padding'])
        self.port_entry = tk.Entry(config_frame, textvariable=self.port_var, 
                                  width=LAYOUT['entry_width'], **STYLES['entry'])
        self.port_entry.grid(row=1, column=1, sticky="ew", padx=(LAYOUT['small_padding'], 0), pady=LAYOUT['small_padding'])
        
        config_frame.grid_columnconfigure(1, weight=1)
        
        # Control section
        control_label = tk.Label(left_frame, text="Server Control", **STYLES['subheading'])
        control_label.grid(row=2, column=0, columnspan=2, sticky="w", 
                          padx=LAYOUT['padding'], pady=(LAYOUT['large_padding'], LAYOUT['small_padding']))
        
        control_frame = tk.Frame(left_frame, **STYLES['frame'])
        control_frame.grid(row=3, column=0, columnspan=2, sticky="ew", 
                          padx=LAYOUT['padding'], pady=LAYOUT['small_padding'])
        
        # Control buttons
        self.start_button = tk.Button(control_frame, text="Start Server", 
                                     command=self.start_server, 
                                     width=LAYOUT['button_width'], **STYLES['button_success'])
        self.start_button.grid(row=0, column=0, padx=(0, LAYOUT['small_padding']), pady=LAYOUT['small_padding'])
        
        self.stop_button = tk.Button(control_frame, text="Stop Server", 
                                    command=self.stop_server, 
                                    width=LAYOUT['button_width'], **STYLES['button_danger'])
        self.stop_button.grid(row=0, column=1, padx=LAYOUT['small_padding'], pady=LAYOUT['small_padding'])
        self.stop_button.configure(state='disabled')
        
        # Data transmission section
        data_label = tk.Label(left_frame, text="Data Transmission", **STYLES['subheading'])
        data_label.grid(row=4, column=0, columnspan=2, sticky="w", 
                       padx=LAYOUT['padding'], pady=(LAYOUT['large_padding'], LAYOUT['small_padding']))
        
        data_frame = tk.Frame(left_frame, **STYLES['frame'])
        data_frame.grid(row=5, column=0, columnspan=2, sticky="ew", 
                       padx=LAYOUT['padding'], pady=LAYOUT['small_padding'])
        data_frame.grid_columnconfigure(0, weight=1)
        
        # Test data input
        tk.Label(data_frame, text="Test Data:", **STYLES['label']).grid(row=0, column=0, sticky="w", pady=LAYOUT['small_padding'])
        self.test_data_entry = tk.Entry(data_frame, **STYLES['entry'])
        self.test_data_entry.grid(row=1, column=0, sticky="ew", pady=LAYOUT['small_padding'])
        self.test_data_entry.insert(0, "$GPGGA,123519,4807.038,N,01131.000,E,1,08,0.9,545.4,M,46.9,M,,*47")
        
        # Send button
        self.send_button = tk.Button(data_frame, text="Send Test Data", 
                                    command=self.send_test_data, 
                                    **STYLES['button_primary'])
        self.send_button.grid(row=2, column=0, pady=LAYOUT['small_padding'])
        self.send_button.configure(state='disabled')
    
    def create_right_panel(self, parent):
        """Create the right panel with status and logs"""
        right_frame = tk.Frame(parent, **STYLES['panel_frame'])
        right_frame.grid(row=0, column=1, sticky="nsew", padx=(LAYOUT['small_padding'], 0))
        right_frame.grid_rowconfigure(2, weight=1)
        right_frame.grid_columnconfigure(0, weight=1)
        
        # Status section
        status_label = tk.Label(right_frame, text="Server Status", **STYLES['subheading'])
        status_label.grid(row=0, column=0, sticky="w", 
                         padx=LAYOUT['padding'], pady=(LAYOUT['padding'], LAYOUT['small_padding']))
        
        status_frame = tk.Frame(right_frame, **STYLES['frame'])
        status_frame.grid(row=1, column=0, sticky="ew", 
                         padx=LAYOUT['padding'], pady=LAYOUT['small_padding'])
        status_frame.grid_columnconfigure(1, weight=1)
        
        # Status indicators and labels
        self.create_status_row(status_frame, 0, "Server:", "server_status", "server_label")
        self.create_status_row(status_frame, 1, "Client:", "client_status", "client_label")
        self.create_status_row(status_frame, 2, "Address:", "address_status", "address_label")
        self.create_status_row(status_frame, 3, "Data Sent:", "data_status", "data_label")
        
        # Log section
        log_label = tk.Label(right_frame, text="Activity Log", **STYLES['subheading'])
        log_label.grid(row=2, column=0, sticky="w", 
                      padx=LAYOUT['padding'], pady=(LAYOUT['large_padding'], LAYOUT['small_padding']))
        
        # Log text area with scrollbar
        log_frame = tk.Frame(right_frame, **STYLES['frame'])
        log_frame.grid(row=3, column=0, sticky="nsew", 
                      padx=LAYOUT['padding'], pady=(0, LAYOUT['padding']))
        log_frame.grid_rowconfigure(0, weight=1)
        log_frame.grid_columnconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, **STYLES['text'])
        self.log_text.grid(row=0, column=0, sticky="nsew")
        
        # Clear log button
        clear_button = tk.Button(right_frame, text="Clear Log", 
                                command=self.clear_log, **STYLES['button'])
        clear_button.grid(row=4, column=0, padx=LAYOUT['padding'], pady=LAYOUT['small_padding'])
    
    def create_status_row(self, parent, row, label_text, indicator_attr, label_attr):
        """Create a status row with indicator and label"""
        tk.Label(parent, text=label_text, **STYLES['label']).grid(row=row, column=0, sticky="w", pady=LAYOUT['small_padding'])
        
        indicator = StatusIndicator(parent)
        indicator.grid(row=row, column=1, padx=LAYOUT['small_padding'], pady=LAYOUT['small_padding'])
        setattr(self, indicator_attr, indicator)
        
        label = tk.Label(parent, text="Idle", **STYLES['label'])
        label.grid(row=row, column=2, sticky="w", padx=LAYOUT['small_padding'], pady=LAYOUT['small_padding'])
        setattr(self, label_attr, label)

    def setup_styles(self):
        """Apply additional styling and configure widget behaviors"""
        # Configure scrollbar styling
        style = ttk.Style()
        style.theme_use('clam')

        # Configure ttk styles for dark theme
        style.configure('Vertical.TScrollbar',
                       background=COLORS['bg_secondary'],
                       troughcolor=COLORS['bg_primary'],
                       bordercolor=COLORS['border_primary'],
                       arrowcolor=COLORS['text_secondary'],
                       darkcolor=COLORS['bg_secondary'],
                       lightcolor=COLORS['bg_secondary'])

    def log_message(self, message: str, level: str = "INFO"):
        """Add a message to the activity log"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # Limit log size to prevent memory issues
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 1000:
            self.log_text.delete("1.0", f"{len(lines)-500}.0")

    def clear_log(self):
        """Clear the activity log"""
        self.log_text.delete("1.0", tk.END)
        self.log_message("Log cleared")

    def validate_config(self) -> bool:
        """Validate the server configuration"""
        try:
            port = int(self.port_var.get())
            if not (1 <= port <= 65535):
                raise ValueError("Port must be between 1 and 65535")

            host = self.host_var.get().strip()
            if not host:
                raise ValueError("Host cannot be empty")

            return True
        except ValueError as e:
            messagebox.showerror("Configuration Error", str(e))
            return False

    def start_server(self):
        """Start the TCP/IP server"""
        if not self.validate_config():
            return

        try:
            config = {
                "host": self.host_var.get().strip(),
                "tcp_port": int(self.port_var.get())
            }

            self.handler = TcpIpOutputHandler(config)

            # Start server in a separate thread to avoid blocking GUI
            def start_server_thread():
                try:
                    if self.handler.open():
                        self.log_message(f"Server started on {config['host']}:{config['tcp_port']}")
                        self.running = True

                        # Update GUI state
                        self.root.after(0, self.on_server_started)

                        # Start status monitoring
                        self.start_status_monitoring()
                    else:
                        self.log_message("Failed to start server", "ERROR")
                        self.root.after(0, self.on_server_start_failed)
                except Exception as e:
                    self.log_message(f"Error starting server: {e}", "ERROR")
                    self.root.after(0, self.on_server_start_failed)

            threading.Thread(target=start_server_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start server: {e}")
            self.log_message(f"Failed to start server: {e}", "ERROR")

    def stop_server(self):
        """Stop the TCP/IP server"""
        self.running = False

        if self.handler:
            try:
                self.handler.close()
                self.log_message("Server stopped")
            except Exception as e:
                self.log_message(f"Error stopping server: {e}", "ERROR")
            finally:
                self.handler = None

        self.on_server_stopped()

    def on_server_started(self):
        """Update GUI when server starts"""
        self.start_button.configure(state='disabled')
        self.stop_button.configure(state='normal')
        self.send_button.configure(state='normal')
        self.host_entry.configure(state='disabled')
        self.port_entry.configure(state='disabled')

    def on_server_start_failed(self):
        """Update GUI when server fails to start"""
        self.handler = None
        self.running = False

    def on_server_stopped(self):
        """Update GUI when server stops"""
        self.start_button.configure(state='normal')
        self.stop_button.configure(state='disabled')
        self.send_button.configure(state='disabled')
        self.host_entry.configure(state='normal')
        self.port_entry.configure(state='normal')

        # Reset status indicators
        self.server_status.set_status('idle')
        self.client_status.set_status('idle')
        self.address_status.set_status('idle')
        self.data_status.set_status('idle')

        self.server_label.configure(text="Idle")
        self.client_label.configure(text="Idle")
        self.address_label.configure(text="Idle")
        self.data_label.configure(text=f"Sent: {self.data_sent_count}")

    def send_test_data(self):
        """Send test data to connected clients"""
        if not self.handler:
            return

        test_data = self.test_data_entry.get().strip()
        if not test_data:
            messagebox.showwarning("Warning", "Please enter test data to send")
            return

        try:
            # Add CRLF line ending if not present
            if not test_data.endswith('\r\n'):
                test_data += '\r\n'

            data_bytes = test_data.encode('utf-8')

            if self.handler.send_data(data_bytes):
                self.data_sent_count += 1
                self.log_message(f"Sent data: {test_data.strip()}")
                self.data_label.configure(text=f"Sent: {self.data_sent_count}")
            else:
                self.log_message("Failed to send data - no client connected", "WARNING")
        except Exception as e:
            self.log_message(f"Error sending data: {e}", "ERROR")

    def start_status_monitoring(self):
        """Start monitoring server status in a separate thread"""
        def monitor_status():
            while self.running and self.handler:
                try:
                    status = self.handler.get_status()
                    self.root.after(0, lambda: self.update_status_display(status))
                    time.sleep(1)  # Update every second
                except Exception as e:
                    self.root.after(0, lambda: self.log_message(f"Status monitoring error: {e}", "ERROR"))
                    break

        self.status_update_thread = threading.Thread(target=monitor_status, daemon=True)
        self.status_update_thread.start()

    def update_status_display(self, status: Dict[str, Any]):
        """Update the status display with current server status"""
        try:
            # Server status
            if status.get('listening', False):
                self.server_status.set_status('listening')
                self.server_label.configure(text="Listening")
            else:
                self.server_status.set_status('disconnected')
                self.server_label.configure(text="Not Listening")

            # Client status
            if status.get('client_connected', False):
                self.client_status.set_status('connected')
                self.client_label.configure(text="Connected")
            else:
                self.client_status.set_status('disconnected')
                self.client_label.configure(text="No Client")

            # Client address
            client_addr = status.get('client_address', 'N/A')
            if client_addr != 'N/A':
                self.address_status.set_status('connected')
                self.address_label.configure(text=client_addr)
            else:
                self.address_status.set_status('idle')
                self.address_label.configure(text="N/A")

            # Data sent count
            self.data_label.configure(text=f"Sent: {self.data_sent_count}")

        except Exception as e:
            self.log_message(f"Error updating status display: {e}", "ERROR")

    def on_closing(self):
        """Handle application closing"""
        if self.handler:
            self.stop_server()
        self.root.destroy()

    def run(self):
        """Start the GUI application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("TCP/IP Output Handler GUI started")
        self.root.mainloop()
