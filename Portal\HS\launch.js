const { exec, spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Check if package.json exists
const checkProjectSetup = () => {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.error('❌ package.json not found. Make sure you are in the correct directory.');
    return false;
  }
  
  // Read package.json to verify it has the necessary scripts
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    if (!packageJson.scripts || !packageJson.scripts.start) {
      console.error('❌ No "start" script found in package.json.');
      return false;
    }
    return true;
  } catch (error) {
    console.error('❌ Error reading package.json:', error.message);
    return false;
  }
};

// Check for node_modules
const checkDependencies = () => {
  const nodeModulesPath = path.join(process.cwd(), 'node_modules');
  
  if (!fs.existsSync(nodeModulesPath)) {
    console.warn('⚠️ node_modules directory not found. Dependencies may not be installed.');
    return false;
  }
  return true;
};

// Determine the correct command based on available package managers
const checkPackageManager = () => {
  return new Promise((resolve) => {
    exec('which yarn', (error) => {
      if (error) {
        // Check if npm exists
        exec('which npm', (npmError) => {
          if (npmError) {
            console.error('❌ Neither yarn nor npm found in PATH.');
            resolve(null);
          } else {
            resolve('npm');
          }
        });
      } else {
        resolve('yarn');
      }
    });
  });
};

const installDependencies = async (packageManager) => {
  console.log('📦 Installing dependencies...');
  
  return new Promise((resolve, reject) => {
    const installCommand = packageManager === 'yarn' ? 'yarn' : 'npm install';
    
    const child = exec(installCommand, (error) => {
      if (error) {
        console.error('❌ Failed to install dependencies:', error.message);
        reject(error);
        return;
      }
      console.log('✅ Dependencies installed successfully.');
      resolve();
    });
    
    child.stdout.pipe(process.stdout);
    child.stderr.pipe(process.stderr);
  });
};

const launchApp = async () => {
  console.log('🔍 Checking project setup...');
  
  // Check project setup
  if (!checkProjectSetup()) {
    console.error('❌ Project setup issues detected. Please fix them before launching.');
    return;
  }
  
  // Check dependencies
  const dependenciesInstalled = checkDependencies();
  
  // Check package manager
  const packageManager = await checkPackageManager();
  if (!packageManager) {
    console.error('❌ No package manager found. Please install npm or yarn.');
    return;
  }
  
  // Install dependencies if needed
  if (!dependenciesInstalled) {
    try {
      await installDependencies(packageManager);
    } catch (error) {
      console.error('❌ Failed to install dependencies. Try installing them manually.');
      return;
    }
  }
  
  console.log('🚀 Launching application...');
  
  const startCommand = packageManager === 'yarn' ? 'yarn' : 'npm';
  const args = packageManager === 'yarn' ? ['start'] : ['run', 'start'];
  
  // Use spawn instead of exec for better handling of the child process
  const child = spawn(startCommand, args, { 
    stdio: 'pipe',
    shell: true
  });
  
  let serverStarted = false;
  let url = null;
  
  child.stdout.on('data', (data) => {
    const output = data.toString();
    console.log(output);
    
    // If we see that the server has started, open the browser
    if (!serverStarted && (output.includes('Local:') || output.includes('localhost:'))) {
      serverStarted = true;
      
      // Try to extract the URL
      const urlMatch = output.match(/(https?:\/\/localhost:[0-9]+)/);
      if (urlMatch) {
        url = urlMatch[1];
        console.log(`🌐 Server started at ${url}`);
        
        // Open browser based on platform
        const openCommand = process.platform === 'win32' ? 'start' : 
                           process.platform === 'darwin' ? 'open' : 'xdg-open';
        
        exec(`${openCommand} ${url}`);
      } else {
        console.log('🌐 Server started, but couldn\'t determine the URL.');
      }
    }
  });
  
  child.stderr.on('data', (data) => {
    console.error(`⚠️ ${data.toString()}`);
  });
  
  child.on('error', (error) => {
    console.error('❌ Failed to start the application:', error.message);
  });
  
  child.on('close', (code) => {
    if (code !== 0) {
      console.error(`❌ Application exited with code ${code}`);
    } else {
      console.log('✅ Application closed successfully.');
    }
  });
  
  // Handle termination signals
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping the application...');
    child.kill('SIGINT');
  });
};

launchApp();