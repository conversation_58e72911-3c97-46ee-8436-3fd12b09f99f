import React, { useState } from 'react';
import { 
  Ship, ChevronRight, ArrowUp, ArrowDown, 
  Database, BarChart2, FileText, Layers,
  Upload, Download, Save, Settings, 
  AlertTriangle, User, Bell
} from 'lucide-react';
import { 
  LineChart, Line, XAxis, YAxis, CartesianGrid, 
  Tooltip, ResponsiveContainer, ReferenceLine 
} from 'recharts';

const SVPEnhanced = () => {
  // Tab and mode states
  const [activeTab, setActiveTab] = useState('graph');
  const [castDirection, setCastDirection] = useState('downcast');
  const [compareMode, setCompareMode] = useState(false);
  const [selectedProfiles, setSelectedProfiles] = useState(['profile-1']);
  
  // Display option states
  const [showVelocity, setShowVelocity] = useState(true);
  const [showTemperature, setShowTemperature] = useState(true);
  const [showSalinity, setShowSalinity] = useState(true);
  const [showGridLines, setShowGridLines] = useState(true);
  const [showLayerMarkers, setShowLayerMarkers] = useState(true);
  
  // Mock profiles
  const profiles = [
    { id: 'profile-1', name: 'SVP_20250508_0930', timestamp: '2025-05-08', status: 'Verified', color: '#10b981' },
    { id: 'profile-2', name: 'SVP_20250507_1645', timestamp: '2025-05-07', status: 'Approved', color: '#f97316' },
    { id: 'profile-3', name: 'SVP_20250506_1215', timestamp: '2025-05-06', status: 'Archived', color: '#6366f1' }
  ];
  
  // SVP data for each profile
  const svpData = {
    'profile-1': {
      downcast: [
        { depth: 0, velocity: 1510, temp: 15.0, salinity: 35.0 },
        { depth: 10, velocity: 1507, temp: 14.7, salinity: 35.0 },
        { depth: 20, velocity: 1505, temp: 14.3, salinity: 35.0 },
        { depth: 30, velocity: 1500, temp: 13.8, salinity: 35.1 },
        { depth: 50, velocity: 1495, temp: 12.5, salinity: 35.1 },
        { depth: 75, velocity: 1490, temp: 10.2, salinity: 35.2 },
        { depth: 100, velocity: 1487, temp: 9.0, salinity: 35.2 },
        { depth: 150, velocity: 1485, temp: 8.0, salinity: 35.3 },
        { depth: 200, velocity: 1483, temp: 7.2, salinity: 35.4 },
        { depth: 300, velocity: 1490, temp: 6.5, salinity: 35.5 }
      ],
      upcast: [
        { depth: 0, velocity: 1510, temp: 15.1, salinity: 35.0 },
        { depth: 10, velocity: 1507, temp: 14.8, salinity: 35.0 },
        { depth: 20, velocity: 1505, temp: 14.4, salinity: 35.0 },
        { depth: 30, velocity: 1500, temp: 13.9, salinity: 35.1 },
        { depth: 50, velocity: 1495, temp: 12.6, salinity: 35.1 },
        { depth: 75, velocity: 1490, temp: 10.3, salinity: 35.2 },
        { depth: 100, velocity: 1487, temp: 9.1, salinity: 35.2 },
        { depth: 150, velocity: 1485, temp: 8.1, salinity: 35.3 },
        { depth: 200, velocity: 1483, temp: 7.3, salinity: 35.4 },
        { depth: 300, velocity: 1490, temp: 6.6, salinity: 35.5 }
      ]
    },
    'profile-2': {
      downcast: [
        { depth: 0, velocity: 1508, temp: 14.8, salinity: 34.9 },
        { depth: 10, velocity: 1505, temp: 14.5, salinity: 34.9 },
        { depth: 20, velocity: 1503, temp: 14.1, salinity: 34.9 },
        { depth: 30, velocity: 1498, temp: 13.6, salinity: 35.0 },
        { depth: 50, velocity: 1493, temp: 12.3, salinity: 35.0 },
        { depth: 75, velocity: 1488, temp: 10.0, salinity: 35.1 },
        { depth: 100, velocity: 1485, temp: 8.8, salinity: 35.1 },
        { depth: 150, velocity: 1483, temp: 7.8, salinity: 35.2 },
        { depth: 200, velocity: 1481, temp: 7.0, salinity: 35.3 },
        { depth: 300, velocity: 1488, temp: 6.3, salinity: 35.4 }
      ],
      upcast: [
        { depth: 0, velocity: 1508, temp: 14.9, salinity: 34.9 },
        { depth: 10, velocity: 1505, temp: 14.6, salinity: 34.9 },
        { depth: 20, velocity: 1503, temp: 14.2, salinity: 34.9 },
        { depth: 30, velocity: 1498, temp: 13.7, salinity: 35.0 },
        { depth: 50, velocity: 1493, temp: 12.4, salinity: 35.0 },
        { depth: 75, velocity: 1488, temp: 10.1, salinity: 35.1 },
        { depth: 100, velocity: 1485, temp: 8.9, salinity: 35.1 },
        { depth: 150, velocity: 1483, temp: 7.9, salinity: 35.2 },
        { depth: 200, velocity: 1481, temp: 7.1, salinity: 35.3 },
        { depth: 300, velocity: 1488, temp: 6.4, salinity: 35.4 }
      ]
    },
    'profile-3': {
      downcast: [
        { depth: 0, velocity: 1512, temp: 15.2, salinity: 35.1 },
        { depth: 10, velocity: 1509, temp: 14.9, salinity: 35.1 },
        { depth: 20, velocity: 1507, temp: 14.5, salinity: 35.1 },
        { depth: 30, velocity: 1502, temp: 14.0, salinity: 35.2 },
        { depth: 50, velocity: 1497, temp: 12.7, salinity: 35.2 },
        { depth: 75, velocity: 1492, temp: 10.4, salinity: 35.3 },
        { depth: 100, velocity: 1489, temp: 9.2, salinity: 35.3 },
        { depth: 150, velocity: 1487, temp: 8.2, salinity: 35.4 },
        { depth: 200, velocity: 1485, temp: 7.4, salinity: 35.5 },
        { depth: 300, velocity: 1492, temp: 6.7, salinity: 35.6 }
      ],
      upcast: [
        { depth: 0, velocity: 1512, temp: 15.3, salinity: 35.1 },
        { depth: 10, velocity: 1509, temp: 15.0, salinity: 35.1 },
        { depth: 20, velocity: 1507, temp: 14.6, salinity: 35.1 },
        { depth: 30, velocity: 1502, temp: 14.1, salinity: 35.2 },
        { depth: 50, velocity: 1497, temp: 12.8, salinity: 35.2 },
        { depth: 75, velocity: 1492, temp: 10.5, salinity: 35.3 },
        { depth: 100, velocity: 1489, temp: 9.3, salinity: 35.3 },
        { depth: 150, velocity: 1487, temp: 8.3, salinity: 35.4 },
        { depth: 200, velocity: 1485, temp: 7.5, salinity: 35.5 },
        { depth: 300, velocity: 1492, temp: 6.8, salinity: 35.6 }
      ]
    }
  };
  
  // Parameter definitions
  const parameters = [
    { id: 'velocity', name: 'Sound Speed', unit: 'm/s', color: '#e11d48', domain: [1480, 1520] },
    { id: 'temp', name: 'Temperature', unit: '°C', color: '#10b981', domain: [6, 16] },
    { id: 'salinity', name: 'Salinity', unit: 'PSU', color: '#3b82f6', domain: [34.5, 36] }
  ];
  
  // Get data for the currently selected profiles and cast direction
  const getData = () => {
    if (compareMode) {
      let result = [];
      selectedProfiles.forEach(profileId => {
        const profileData = svpData[profileId][castDirection].map(item => ({
          ...item,
          profileId
        }));
        result = result.concat(profileData);
      });
      return result;
    } else {
      return svpData[selectedProfiles[0]][castDirection];
    }
  };
  
  // Toggle profile selection
  const toggleProfile = (profileId) => {
    if (selectedProfiles.includes(profileId)) {
      if (selectedProfiles.length > 1) {
        setSelectedProfiles(selectedProfiles.filter(id => id !== profileId));
      }
    } else {
      setSelectedProfiles([...selectedProfiles, profileId]);
    }
  };
  
  // Get status color
  const getStatusColor = (status) => {
    switch(status) {
      case 'Verified': return 'bg-green-100 text-green-800';
      case 'Approved': return 'bg-blue-100 text-blue-800';
      case 'Archived': return 'bg-gray-100 text-gray-800';
      default: return 'bg-amber-100 text-amber-800';
    }
  };
  
  // Custom tooltip
  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-gray-800 text-white p-2 rounded-md shadow text-xs">
          <p className="font-bold">Depth: {data.depth}m</p>
          {showVelocity && <p>Sound Speed: {data.velocity.toFixed(1)} m/s</p>}
          {showTemperature && <p>Temperature: {data.temp.toFixed(1)} °C</p>}
          {showSalinity && <p>Salinity: {data.salinity.toFixed(2)} PSU</p>}
          {data.profileId && (
            <p className="mt-1 pt-1 border-t border-gray-600">
              {profiles.find(p => p.id === data.profileId)?.name}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  // Render profile line for velocity graph
  const renderVelocityLines = () => {
    if (compareMode) {
      return selectedProfiles.map(profileId => {
        const profile = profiles.find(p => p.id === profileId);
        if (!profile || !showVelocity) return null;
        
        return (
          <Line
            key={`${profileId}-velocity`}
            type="monotone"
            dataKey="velocity"
            data={getData().filter(d => d.profileId === profileId)}
            stroke={profile.color}
            strokeWidth={1.5}
            dot={false}
            activeDot={{ r: 4 }}
            name={`${profile.name} - Velocity`}
          />
        );
      });
    } else {
      if (!showVelocity) return null;
      
      return (
        <Line
          type="monotone"
          dataKey="velocity"
          stroke={parameters.find(p => p.id === 'velocity').color}
          strokeWidth={1.5}
          dot={false}
          activeDot={{ r: 4 }}
        />
      );
    }
  };
  
  // Render profile line for temperature graph
  const renderTemperatureLines = () => {
    if (compareMode) {
      return selectedProfiles.map(profileId => {
        const profile = profiles.find(p => p.id === profileId);
        if (!profile || !showTemperature) return null;
        
        return (
          <Line
            key={`${profileId}-temp`}
            type="monotone"
            dataKey="temp"
            data={getData().filter(d => d.profileId === profileId)}
            stroke={profile.color}
            strokeWidth={1.5}
            dot={false}
            activeDot={{ r: 4 }}
            name={`${profile.name} - Temperature`}
          />
        );
      });
    } else {
      if (!showTemperature) return null;
      
      return (
        <Line
          type="monotone"
          dataKey="temp"
          stroke={parameters.find(p => p.id === 'temp').color}
          strokeWidth={1.5}
          dot={false}
          activeDot={{ r: 4 }}
        />
      );
    }
  };
  
  // Render profile line for salinity graph
  const renderSalinityLines = () => {
    if (compareMode) {
      return selectedProfiles.map(profileId => {
        const profile = profiles.find(p => p.id === profileId);
        if (!profile || !showSalinity) return null;
        
        return (
          <Line
            key={`${profileId}-salinity`}
            type="monotone"
            dataKey="salinity"
            data={getData().filter(d => d.profileId === profileId)}
            stroke={profile.color}
            strokeWidth={1.5}
            dot={false}
            activeDot={{ r: 4 }}
            name={`${profile.name} - Salinity`}
          />
        );
      });
    } else {
      if (!showSalinity) return null;
      
      return (
        <Line
          type="monotone"
          dataKey="salinity"
          stroke={parameters.find(p => p.id === 'salinity').color}
          strokeWidth={1.5}
          dot={false}
          activeDot={{ r: 4 }}
        />
      );
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-100">
      {/* Sidebar */}
      <aside className="bg-gray-900 text-white w-16 flex flex-col">
        <div className="p-4 border-b border-gray-800 flex justify-center">
          <Ship className="text-green-400" size={24} />
        </div>
        <div className="flex-grow p-2 space-y-1">
          <button className="w-full p-2 rounded-md hover:bg-gray-800 flex justify-center">
            <Database size={20} className="text-gray-400" />
          </button>
          <button className="w-full p-2 rounded-md bg-green-600 flex justify-center">
            <BarChart2 size={20} className="text-white" />
          </button>
          <button className="w-full p-2 rounded-md hover:bg-gray-800 flex justify-center">
            <FileText size={20} className="text-gray-400" />
          </button>
        </div>
      </aside>
      
      {/* Main content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-white shadow-sm p-4">
          <div className="flex items-center text-sm">
            <span className="text-gray-500">Fleet</span>
            <ChevronRight size={16} className="mx-2 text-gray-400" />
            <span className="text-gray-500">Normand Energy</span>
            <ChevronRight size={16} className="mx-2 text-gray-400" />
            <span className="text-gray-900 font-medium">SVP Manager</span>
          </div>
        </header>
        
        {/* Main content */}
        <main className="flex-grow p-4">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* Left panel - profiles and settings */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md h-full">
                {/* Header */}
                <div className="px-4 py-3 border-b border-gray-200">
                  <h2 className="font-bold text-gray-800">SVP Profiles</h2>
                </div>
                
                {/* Actions */}
                <div className="p-4 border-b border-gray-200">
                  <div className="flex space-x-2 mb-3">
                    <button className="flex-1 bg-green-500 text-white rounded-md py-2 px-3 text-sm flex items-center justify-center">
                      <Upload size={16} className="mr-2" />
                      Import
                    </button>
                    <button className="flex-1 bg-gray-200 text-gray-700 rounded-md py-2 px-3 text-sm flex items-center justify-center">
                      <Download size={16} className="mr-2" />
                      Export
                    </button>
                  </div>
                </div>
                
                {/* Display options */}
                <div className="p-4 border-b border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Display Options</h3>
                  
                  {/* Cast direction */}
                  <div className="mb-3">
                    <label className="text-xs text-gray-500 mb-1 block">Cast Direction</label>
                    <div className="flex bg-gray-100 rounded-md overflow-hidden">
                      <button 
                        className={`flex-1 py-2 flex items-center justify-center text-sm ${
                          castDirection === 'downcast' ? 'bg-blue-500 text-white' : 'text-gray-700'
                        }`}
                        onClick={() => setCastDirection('downcast')}
                      >
                        <ArrowDown size={16} className="mr-1" />
                        Downcast
                      </button>
                      <button 
                        className={`flex-1 py-2 flex items-center justify-center text-sm ${
                          castDirection === 'upcast' ? 'bg-blue-500 text-white' : 'text-gray-700'
                        }`}
                        onClick={() => setCastDirection('upcast')}
                      >
                        <ArrowUp size={16} className="mr-1" />
                        Upcast
                      </button>
                    </div>
                  </div>
                  
                  {/* Compare mode */}
                  <div className="mb-3">
                    <label className="text-xs text-gray-500 mb-1 block">Profile Mode</label>
                    <div className="flex bg-gray-100 rounded-md overflow-hidden">
                      <button 
                        className={`flex-1 py-2 text-sm ${
                          !compareMode ? 'bg-blue-500 text-white' : 'text-gray-700'
                        }`}
                        onClick={() => setCompareMode(false)}
                      >
                        Single
                      </button>
                      <button 
                        className={`flex-1 py-2 text-sm ${
                          compareMode ? 'bg-blue-500 text-white' : 'text-gray-700'
                        }`}
                        onClick={() => setCompareMode(true)}
                      >
                        Compare
                      </button>
                    </div>
                  </div>
                  
                  {/* Parameters */}
                  <div className="mb-3">
                    <label className="text-xs text-gray-500 mb-1 block">Parameters</label>
                    <div className="space-y-1">
                      <div className="flex items-center">
                        <input 
                          type="checkbox" 
                          id="velocity-checkbox" 
                          checked={showVelocity} 
                          onChange={() => setShowVelocity(!showVelocity)}
                          className="h-4 w-4 rounded border-gray-300"
                        />
                        <label htmlFor="velocity-checkbox" className="ml-2 text-sm text-gray-700 flex items-center">
                          <span className="inline-block h-2 w-2 rounded-full bg-red-500 mr-2"></span>
                          Sound Speed (m/s)
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input 
                          type="checkbox" 
                          id="temp-checkbox" 
                          checked={showTemperature} 
                          onChange={() => setShowTemperature(!showTemperature)}
                          className="h-4 w-4 rounded border-gray-300"
                        />
                        <label htmlFor="temp-checkbox" className="ml-2 text-sm text-gray-700 flex items-center">
                          <span className="inline-block h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                          Temperature (°C)
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input 
                          type="checkbox" 
                          id="salinity-checkbox" 
                          checked={showSalinity} 
                          onChange={() => setShowSalinity(!showSalinity)}
                          className="h-4 w-4 rounded border-gray-300"
                        />
                        <label htmlFor="salinity-checkbox" className="ml-2 text-sm text-gray-700 flex items-center">
                          <span className="inline-block h-2 w-2 rounded-full bg-blue-500 mr-2"></span>
                          Salinity (PSU)
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  {/* Additional options */}
                  <div className="space-y-1">
                    <div className="flex items-center">
                      <input 
                        type="checkbox" 
                        id="grid-checkbox" 
                        checked={showGridLines} 
                        onChange={() => setShowGridLines(!showGridLines)}
                        className="h-4 w-4 rounded border-gray-300"
                      />
                      <label htmlFor="grid-checkbox" className="ml-2 text-sm text-gray-700">
                        Show Grid Lines
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input 
                        type="checkbox" 
                        id="layers-checkbox" 
                        checked={showLayerMarkers} 
                        onChange={() => setShowLayerMarkers(!showLayerMarkers)}
                        className="h-4 w-4 rounded border-gray-300"
                      />
                      <label htmlFor="layers-checkbox" className="ml-2 text-sm text-gray-700">
                        Show Layer Boundaries
                      </label>
                    </div>
                  </div>
                </div>
                
                {/* Profile list */}
                <div className="p-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">Available Profiles</h3>
                  <div className="space-y-2 overflow-y-auto max-h-64">
                    {profiles.map(profile => (
                      <div 
                        key={profile.id}
                        className={`p-3 border rounded-md cursor-pointer transition-colors ${
                          selectedProfiles.includes(profile.id) 
                            ? 'border-blue-500 bg-blue-50' 
                            : 'border-gray-200 hover:bg-gray-50'
                        }`}
                        onClick={() => compareMode ? toggleProfile(profile.id) : setSelectedProfiles([profile.id])}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div 
                              className="w-3 h-3 rounded-full mr-2" 
                              style={{ backgroundColor: profile.color }}
                            ></div>
                            <span className="text-sm font-medium">{profile.name}</span>
                          </div>
                          <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(profile.status)}`}>
                            {profile.status}
                          </span>
                        </div>
                        <div className="mt-1 text-xs text-gray-500">
                          Acquired: {profile.timestamp}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            
            {/* Right panel - visualization */}
            <div className="lg:col-span-3">
              <div className="bg-white rounded-lg shadow-md h-full">
                {/* Tabs */}
                <div className="border-b border-gray-200">
                  <div className="flex px-4">
                    <button 
                      className={`py-3 px-4 border-b-2 text-sm font-medium ${
                        activeTab === 'graph' 
                          ? 'border-green-500 text-green-600' 
                          : 'border-transparent text-gray-500 hover:text-gray-700'
                      }`}
                      onClick={() => setActiveTab('graph')}
                    >
                      Graph View
                    </button>
                    <button 
                      className={`py-3 px-4 border-b-2 text-sm font-medium ${
                        activeTab === 'table' 
                          ? 'border-green-500 text-green-600' 
                          : 'border-transparent text-gray-500 hover:text-gray-700'
                      }`}
                      onClick={() => setActiveTab('table')}
                    >
                      Table View
                    </button>
                    <button 
                      className={`py-3 px-4 border-b-2 text-sm font-medium ${
                        activeTab === 'quality' 
                          ? 'border-green-500 text-green-600' 
                          : 'border-transparent text-gray-500 hover:text-gray-700'
                      }`}
                      onClick={() => setActiveTab('quality')}
                    >
                      Quality Control
                    </button>
                  </div>
                </div>
                
                {/* Content */}
                <div className="p-4">
                  {activeTab === 'graph' && (
                    <div>
                      {/* Info section */}
                      <div className="mb-3 flex justify-between items-center">
                        <div>
                          <h2 className="text-lg font-bold text-gray-800">
                            {compareMode 
                              ? `Comparing ${selectedProfiles.length} Profiles` 
                              : profiles.find(p => p.id === selectedProfiles[0])?.name
                            }
                          </h2>
                          <p className="text-sm text-gray-600">
                            {castDirection === 'downcast' ? 'Downcast' : 'Upcast'} Data • 
                            Max Depth: 300m
                          </p>
                        </div>
                        
                        {/* Legend for comparison mode */}
                        {compareMode && (
                          <div className="flex space-x-4">
                            {selectedProfiles.map(profileId => {
                              const profile = profiles.find(p => p.id === profileId);
                              if (!profile) return null;
                              
                              return (
                                <div key={profileId} className="flex items-center">
                                  <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: profile.color }}></div>
                                  <span className="text-xs font-medium">{profile.name}</span>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                      
                      {/* Main Graphs Row */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                        {/* Sound Speed Graph */}
                        <div className="bg-gray-50 border rounded p-2">
                          <div className="text-xs font-medium text-center text-gray-700 mb-1">Sound Speed (m/s)</div>
                          <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                              <LineChart
                                data={getData()}
                                margin={{ top: 5, right: 5, left: -20, bottom: 5 }}
                                layout="vertical"
                              >
                                {showGridLines && <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />}
                                
                                <YAxis 
                                  dataKey="depth" 
                                  type="number" 
                                  domain={[0, 300]}
                                  reversed={false}
                                  tick={{ fontSize: 10 }}
                                  tickCount={10}
                                  label={{ value: 'Depth (m)', angle: -90, position: 'insideLeft', style: { fontSize: 10 } }}
                                />
                                
                                <XAxis 
                                  type="number" 
                                  domain={parameters.find(p => p.id === 'velocity').domain}
                                  dataKey="velocity"
                                  tick={{ fontSize: 10 }}
                                  tickCount={5}
                                />
                                
                                <Tooltip content={<CustomTooltip />} />
                                
                                {showLayerMarkers && (
                                  <>
                                    <ReferenceLine y={20} stroke="#f59e0b" strokeDasharray="3 3" />
                                    <ReferenceLine y={100} stroke="#8b5cf6" strokeDasharray="3 3" />
                                  </>
                                )}
                                
                                {renderVelocityLines()}
                              </LineChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                        
                        {/* Temperature Graph */}
                        <div className="bg-gray-50 border rounded p-2">
                          <div className="text-xs font-medium text-center text-gray-700 mb-1">Temperature (°C)</div>
                          <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                              <LineChart
                                data={getData()}
                                margin={{ top: 5, right: 5, left: -20, bottom: 5 }}
                                layout="vertical"
                              >
                                {showGridLines && <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />}
                                
                                <YAxis 
                                  dataKey="depth" 
                                  type="number" 
                                  domain={[0, 300]}
                                  reversed={false}
                                  tick={{ fontSize: 10 }}
                                  tickCount={10}
                                  label={{ value: 'Depth (m)', angle: -90, position: 'insideLeft', style: { fontSize: 10 } }}
                                />
                                
                                <XAxis 
                                  type="number" 
                                  domain={parameters.find(p => p.id === 'temp').domain}
                                  dataKey="temp"
                                  tick={{ fontSize: 10 }}
                                  tickCount={6}
                                />
                                
                                <Tooltip content={<CustomTooltip />} />
                                
                                {showLayerMarkers && (
                                  <>
                                    <ReferenceLine y={20} stroke="#f59e0b" strokeDasharray="3 3" />
                                    <ReferenceLine y={100} stroke="#8b5cf6" strokeDasharray="3 3" />
                                  </>
                                )}
                                
                                {renderTemperatureLines()}
                              </LineChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                        
                        {/* Salinity Graph */}
                        <div className="bg-gray-50 border rounded p-2">
                          <div className="text-xs font-medium text-center text-gray-700 mb-1">Salinity (PSU)</div>
                          <div className="h-80">
                            <ResponsiveContainer width="100%" height="100%">
                              <LineChart
                                data={getData()}
                                margin={{ top: 5, right: 5, left: -20, bottom: 5 }}
                                layout="vertical"
                              >
                                {showGridLines && <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />}
                                
                                <YAxis 
                                  dataKey="depth" 
                                  type="number" 
                                  domain={[0, 300]}
                                  reversed={false}
                                  tick={{ fontSize: 10 }}
                                  tickCount={10}
                                  label={{ value: 'Depth (m)', angle: -90, position: 'insideLeft', style: { fontSize: 10 } }}
                                />
                                
                                <XAxis 
                                  type="number" 
                                  domain={parameters.find(p => p.id === 'salinity').domain}
                                  dataKey="salinity"
                                  tick={{ fontSize: 10 }}
                                  tickCount={4}
                                />
                                
                                <Tooltip content={<CustomTooltip />} />
                                
                                {showLayerMarkers && (
                                  <>
                                    <ReferenceLine y={20} stroke="#f59e0b" strokeDasharray="3 3" />
                                    <ReferenceLine y={100} stroke="#8b5cf6" strokeDasharray="3 3" />
                                  </>
                                )}
                                
                                {renderSalinityLines()}
                              </LineChart>
                            </ResponsiveContainer>
                          </div>
                        </div>
                      </div>
                      
                      {/* Info Cards */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div className="p-3 border rounded-md bg-red-50">
                          <div className="font-medium text-sm">Sound Velocity</div>
                          <div className="text-xs">Range: 1483-1510 m/s</div>
                          <div className="text-xs">Surface: 1510 m/s</div>
                          <div className="text-xs">Minimum at ~150m: 1485 m/s</div>
                        </div>
                        <div className="p-3 border rounded-md bg-green-50">
                          <div className="font-medium text-sm">Temperature</div>
                          <div className="text-xs">Surface: 15.0°C</div>
                          <div className="text-xs">Bottom: 6.5°C</div>
                          <div className="text-xs">Strong thermocline 20-100m</div>
                        </div>
                        <div className="p-3 border rounded-md bg-blue-50">
                          <div className="font-medium text-sm">Salinity</div>
                          <div className="text-xs">Surface: 35.0 PSU</div>
                          <div className="text-xs">Bottom: 35.5 PSU</div>
                          <div className="text-xs">Minimal halocline</div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {activeTab === 'table' && (
                    <div>
                      <div className="flex justify-between items-center mb-3">
                        <h2 className="text-lg font-bold text-gray-800">
                          {compareMode 
                            ? `Data for ${selectedProfiles.length} Profiles` 
                            : `${profiles.find(p => p.id === selectedProfiles[0])?.name} Data Table`
                          }
                        </h2>
                        <button className="px-3 py-1.5 bg-gray-100 border border-gray-300 rounded text-sm flex items-center">
                          <Download size={16} className="mr-2" />
                          Export CSV
                        </button>
                      </div>
                      
                      <div className="border rounded-md overflow-hidden">
                        <div className="max-h-[500px] overflow-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50 sticky top-0">
                              <tr>
                                {compareMode && (
                                  <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Profile
                                  </th>
                                )}
                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Depth (m)
                                </th>
                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Sound Speed (m/s)
                                </th>
                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Temperature (°C)
                                </th>
                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Salinity (PSU)
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {getData().map((point, index) => (
                                <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                  {compareMode && (
                                    <td className="px-4 py-2 text-sm text-gray-500">
                                      <div className="flex items-center">
                                        <div 
                                          className="w-2 h-2 rounded-full mr-2" 
                                          style={{ backgroundColor: profiles.find(p => p.id === point.profileId)?.color }}
                                        ></div>
                                        {profiles.find(p => p.id === point.profileId)?.name}
                                      </div>
                                    </td>
                                  )}
                                  <td className="px-4 py-2 text-sm text-gray-900 font-medium">{point.depth}</td>
                                  <td className="px-4 py-2 text-sm text-gray-900">{point.velocity.toFixed(1)}</td>
                                  <td className="px-4 py-2 text-sm text-gray-900">{point.temp.toFixed(2)}</td>
                                  <td className="px-4 py-2 text-sm text-gray-900">{point.salinity.toFixed(3)}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {activeTab === 'quality' && (
                    <div>
                      <h2 className="text-lg font-bold text-gray-800 mb-3">Quality Control</h2>
                      
                      <div className="bg-white border rounded-md overflow-hidden mb-4">
                        <div className="px-4 py-3 border-b bg-gray-50">
                          <h3 className="font-medium">Quality Assessment</h3>
                        </div>
                        <div className="p-4">
                          <div className="space-y-3">
                            <div className="flex items-center">
                              <div className="h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center mr-3">
                                <span className="text-lg font-medium">✓</span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Data Consistency Check</div>
                                <div className="text-sm text-gray-500">Profile data is consistent with expected values</div>
                              </div>
                            </div>
                            
                            <div className="flex items-center">
                              <div className="h-8 w-8 rounded-full bg-amber-100 text-amber-600 flex items-center justify-center mr-3">
                                <span className="text-lg font-medium">!</span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Spike Detection</div>
                                <div className="text-sm text-gray-500">Minor spikes detected at 75-100m depth range</div>
                              </div>
                            </div>
                            
                            <div className="flex items-center">
                              <div className="h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center mr-3">
                                <span className="text-lg font-medium">✓</span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">Value Range Check</div>
                                <div className="text-sm text-gray-500">All values within expected ranges for the region</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-white border rounded-md overflow-hidden">
                        <div className="px-4 py-3 border-b bg-gray-50">
                          <h3 className="font-medium">Recommended Actions</h3>
                        </div>
                        <div className="p-4">
                          <div className="space-y-3">
                            <div className="flex items-start">
                              <Settings size={18} className="text-gray-400 mr-3 mt-0.5" />
                              <div>
                                <div className="font-medium text-gray-900">Apply Median Filter</div>
                                <div className="text-sm text-gray-500">Recommended to smooth minor spikes in the thermocline region</div>
                                <button className="mt-2 px-3 py-1.5 bg-blue-50 text-blue-700 border border-blue-200 rounded text-sm">
                                  Apply Filter
                                </button>
                              </div>
                            </div>
                            
                            <div className="flex items-start">
                              <Settings size={18} className="text-gray-400 mr-3 mt-0.5" />
                              <div>
                                <div className="font-medium text-gray-900">Update Metadata</div>
                                <div className="text-sm text-gray-500">Update profile metadata with quality assessment results</div>
                                <button className="mt-2 px-3 py-1.5 bg-gray-100 text-gray-700 border border-gray-300 rounded text-sm">
                                  Update Metadata
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </main>
        
        {/* Status bar */}
        <footer className="bg-gray-100 border-t border-gray-200 px-6 py-2 text-xs text-gray-500">
          <div className="flex justify-between">
            <div>RTK Position: 58°52.32'N, 0°07' 44.03"W | Depth: 435.2m</div>
            <div>OpenBridge v6.0.245 | SVP Manager v2.3.1</div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default SVPEnhanced;
                