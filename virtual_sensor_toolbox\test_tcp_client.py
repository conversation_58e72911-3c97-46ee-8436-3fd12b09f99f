#!/usr/bin/env python3
"""
Simple TCP client for testing the TCP/IP Output Handler GUI
"""

import socket
import sys
import time
import threading

class SimpleTcpClient:
    """Simple TCP client for testing the server"""
    
    def __init__(self, host="127.0.0.1", port=50100):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        self.running = False
    
    def connect(self):
        """Connect to the TCP server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            self.connected = True
            print(f"Connected to {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"Failed to connect: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from the server"""
        self.running = False
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        self.connected = False
        print("Disconnected from server")
    
    def listen_for_data(self):
        """Listen for incoming data from the server"""
        def listen_thread():
            buffer = ""
            while self.running and self.connected:
                try:
                    data = self.socket.recv(1024)
                    if not data:
                        print("Server closed connection")
                        self.connected = False
                        break
                    
                    # Decode and process data
                    text = data.decode('utf-8', errors='ignore')
                    buffer += text
                    
                    # Process complete lines
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()
                        if line:
                            timestamp = time.strftime("%H:%M:%S")
                            print(f"[{timestamp}] Received: {line}")
                
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"Error receiving data: {e}")
                    self.connected = False
                    break
        
        if self.connected:
            self.running = True
            self.socket.settimeout(1.0)  # 1 second timeout for recv
            listen_thread = threading.Thread(target=listen_thread, daemon=True)
            listen_thread.start()
            return listen_thread
        return None
    
    def send_data(self, data):
        """Send data to the server"""
        if not self.connected:
            print("Not connected to server")
            return False
        
        try:
            if not data.endswith('\r\n'):
                data += '\r\n'
            self.socket.send(data.encode('utf-8'))
            print(f"Sent: {data.strip()}")
            return True
        except Exception as e:
            print(f"Error sending data: {e}")
            return False

def main():
    """Main client function"""
    print("TCP/IP Test Client")
    print("=" * 30)
    print()
    
    # Get connection details
    host = input("Enter server host (default: 127.0.0.1): ").strip() or "127.0.0.1"
    port_str = input("Enter server port (default: 50100): ").strip() or "50100"
    
    try:
        port = int(port_str)
    except ValueError:
        print("Invalid port number")
        sys.exit(1)
    
    # Create and connect client
    client = SimpleTcpClient(host, port)
    
    if not client.connect():
        sys.exit(1)
    
    # Start listening for data
    listen_thread = client.listen_for_data()
    
    print()
    print("Connected! You can now:")
    print("- Type messages to send to the server")
    print("- Type 'quit' or 'exit' to disconnect")
    print("- Press Ctrl+C to exit")
    print()
    
    try:
        while client.connected:
            try:
                user_input = input("> ").strip()
                
                if user_input.lower() in ['quit', 'exit']:
                    break
                
                if user_input:
                    client.send_data(user_input)
                
            except EOFError:
                break
            except KeyboardInterrupt:
                print("\nExiting...")
                break
    
    finally:
        client.disconnect()
        print("Client stopped")

if __name__ == "__main__":
    main()
