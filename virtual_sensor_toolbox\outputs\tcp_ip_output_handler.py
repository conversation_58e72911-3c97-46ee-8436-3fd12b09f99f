import socket
import threading
from typing import Dict, Any, Optional
from .base_output_handler import BaseOutputHandler

class TcpIpOutputHandler(BaseOutputHandler):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.host = config.get("host", "0.0.0.0")  # Listen on all available interfaces
        self.port = config.get("tcp_port", 50100) # Default from previous config
        self.server_socket: Optional[socket.socket] = None
        self.client_socket: Optional[socket.socket] = None
        self.client_address: Optional[Any] = None
        self._lock = threading.Lock() # For thread-safe access to client_socket

    def open(self) -> bool:
        if self.is_connected:
            print(f"[TcpIpOutputHandler] Already connected/listening on {self.host}:{self.port}")
            return True
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1) # Allow reuse of address
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(1) # Listen for one connection
            print(f"[TcpIpOutputHandler] Server listening on {self.host}:{self.port}. Waiting for a client connection...")
            
            # Set timeout to prevent blocking indefinitely
            self.server_socket.settimeout(10.0) # Timeout for accept in seconds

            try:
                self.client_socket, self.client_address = self.server_socket.accept()
                self.client_socket.setblocking(True) # Make client socket blocking for sends
                self.is_connected = True
                print(f"[TcpIpOutputHandler] Accepted connection from {self.client_address}")
                return True
            except socket.timeout:
                print(f"[TcpIpOutputHandler] No client connected within timeout on {self.host}:{self.port}. Still listening if simulation continues.")
                return False # Indicate that open didn't fully succeed with a client

        except socket.error as e:
            print(f"[TcpIpOutputHandler] Error setting up TCP server on {self.host}:{self.port}: {e}")
            self.close() # Ensure cleanup
            return False

    def close(self) -> None:
        with self._lock:
            if self.client_socket:
                try:
                    self.client_socket.shutdown(socket.SHUT_RDWR)
                except (socket.error, OSError):
                    pass # Ignore errors on shutdown, it might already be closed
                self.client_socket.close()
                print(f"[TcpIpOutputHandler] Closed client connection from {self.client_address}")
                self.client_socket = None
                self.client_address = None
            
            if self.server_socket:
                self.server_socket.close()
                print(f"[TcpIpOutputHandler] Stopped TCP server on {self.host}:{self.port}")
                self.server_socket = None
            
            self.is_connected = False


    def send_data(self, data: bytes) -> bool:
        with self._lock:
            if not self.client_socket or not self.is_connected:
                # Attempt to accept a new client if server socket is still open
                if self.server_socket and not self.client_socket:
                    try:
                        self.server_socket.settimeout(0.1) # Short timeout for accept attempt
                        self.client_socket, self.client_address = self.server_socket.accept()
                        self.client_socket.setblocking(True)
                        self.is_connected = True # is_connected refers to having an active client
                        print(f"[TcpIpOutputHandler] Accepted new client connection from {self.client_address} during send.")
                    except (socket.timeout, socket.error):
                        self.is_connected = False # No active client
                        return False # No client to send to
                else:
                    return False # No client and no server socket

            # Now that we potentially have a client, try sending
            if self.client_socket:
                try:
                    self.client_socket.sendall(data)
                    return True
                except socket.error as e:
                    print(f"[TcpIpOutputHandler] Error sending data to {self.client_address}: {e}. Client likely disconnected.")
                    # Clean up the disconnected client
                    self.client_socket.close()
                    self.client_socket = None
                    self.is_connected = False # Mark as no longer having an active client connection
                    return False
            return False


    def get_status(self) -> Dict[str, Any]:
        return {
            "type": "tcp_ip",
            "host": self.host,
            "port": self.port,
            "listening": self.server_socket is not None,
            "client_connected": self.client_socket is not None and self.is_connected,
            "client_address": str(self.client_address) if self.client_address else "N/A"
        }
