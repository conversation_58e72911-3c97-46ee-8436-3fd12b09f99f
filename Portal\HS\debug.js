const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Running diagnostics...');

// Check Node.js version
console.log('\n📊 Node.js Environment:');
console.log(`Node.js version: ${process.version}`);
console.log(`Platform: ${process.platform}`);

// Check if key files exist
console.log('\n📁 Project Structure:');
const requiredFiles = [
  'package.json',
  'svp-enhanced.tsx',
  'index.js',
  'public/index.html',
  'tailwind.config.js',
  'index.css'
];

requiredFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  console.log(`${fs.existsSync(filePath) ? '✅' : '❌'} ${file}`);
});

// Check package.json content
console.log('\n📦 Package.json Scripts:');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(process.cwd(), 'package.json'), 'utf8'));
  console.log(packageJson.scripts || 'No scripts found');
} catch (error) {
  console.error('❌ Error reading package.json:', error.message);
}

// Check node_modules
console.log('\n📚 Dependencies:');
const nodeModulesPath = path.join(process.cwd(), 'node_modules');
console.log(`${fs.existsSync(nodeModulesPath) ? '✅' : '❌'} node_modules directory`);

// Check for React and other key dependencies
const keyDependencies = ['react', 'react-dom', 'recharts', 'lucide-react'];
keyDependencies.forEach(dep => {
  const depPath = path.join(process.cwd(), 'node_modules', dep);
  console.log(`${fs.existsSync(depPath) ? '✅' : '❌'} ${dep}`);
});

// Try to detect package manager
console.log('\n🔧 Package Manager:');
try {
  const hasYarn = fs.existsSync(path.join(process.cwd(), 'yarn.lock'));
  const hasNpm = fs.existsSync(path.join(process.cwd(), 'package-lock.json'));
  
  if (hasYarn) console.log('✅ Yarn detected (yarn.lock found)');
  if (hasNpm) console.log('✅ NPM detected (package-lock.json found)');
  if (!hasYarn && !hasNpm) console.log('❓ No lock file found, package manager undetermined');
} catch (error) {
  console.error('❌ Error checking for package manager:', error.message);
}

console.log('\n🔍 Diagnostics complete. Use this information to troubleshoot launch issues.');