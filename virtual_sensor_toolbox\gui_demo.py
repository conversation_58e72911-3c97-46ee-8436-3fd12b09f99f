#!/usr/bin/env python3
"""
Demo script showing how to integrate the TCP/IP GUI with sensor simulation
"""

import sys
import os
import threading
import time

# Add the project root to the path for imports
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def run_sensor_simulation():
    """Run the sensor simulation in a separate thread"""
    try:
        from plugins.trimble_bd992_simulator.trimble_bd992_plugin import TrimbleBD992Simulator
        
        print("Starting sensor simulation...")
        
        # Create sensor instance
        sensor_instance = TrimbleBD992Simulator()
        
        # Configure sensor
        sensor_config_overrides = {
            "NMEA0183_TCPIP_PRIMARY": {
                "host": "127.0.0.1",
                "tcp_port": 50100,
                "update_rate_hz": 1.0
            },
            "simulation_parameters": {
                "start_latitude": 34.0522,  # Los Angeles
                "start_longitude": -118.2437,
                "start_altitude": 70.0,
                "movement_model": "static"
            }
        }
        sensor_instance.configure(sensor_config_overrides)
        
        # Start the stream
        primary_stream_id = "NMEA0183_TCPIP_PRIMARY"
        
        if sensor_instance.start_stream(primary_stream_id):
            print(f"Sensor simulation started on 127.0.0.1:50100")
            print("The simulation will generate NMEA data automatically when clients connect.")
            
            try:
                # Keep the simulation running
                while True:
                    status = sensor_instance.get_stream_status(primary_stream_id)
                    if not status.get('running', False):
                        print("Sensor simulation stopped unexpectedly")
                        break
                    time.sleep(5)
                    
            except KeyboardInterrupt:
                print("\nStopping sensor simulation...")
            finally:
                sensor_instance.stop_stream(primary_stream_id)
                print("Sensor simulation stopped")
        else:
            print("Failed to start sensor simulation")
            
    except ImportError as e:
        print(f"Could not import sensor simulator: {e}")
        print("Running GUI without automatic data generation")
    except Exception as e:
        print(f"Error in sensor simulation: {e}")

def main():
    """Main demo function"""
    print("TCP/IP Output Handler - GUI Demo")
    print("=" * 40)
    print()
    print("This demo shows two ways to use the TCP/IP functionality:")
    print("1. GUI-only mode: Manual control and data sending")
    print("2. Integrated mode: GUI + automatic sensor data generation")
    print()
    
    choice = input("Choose mode (1=GUI only, 2=GUI + sensor): ").strip()
    
    if choice == "2":
        print("\nStarting integrated mode...")
        print("The sensor simulation will run in the background.")
        print("Use the GUI to monitor connections and send additional test data.")
        print()
        
        # Start sensor simulation in background thread
        sensor_thread = threading.Thread(target=run_sensor_simulation, daemon=True)
        sensor_thread.start()
        
        # Give the sensor a moment to start
        time.sleep(2)
    
    print("Starting GUI...")
    print("Configure the server to use 127.0.0.1:50100 to match the sensor simulation.")
    print()
    
    try:
        from gui.tcp_ip_gui import TcpIpGui
        
        # Create and run the GUI
        app = TcpIpGui()
        app.run()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure you're running this from the virtual_sensor_toolbox directory")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting GUI: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
